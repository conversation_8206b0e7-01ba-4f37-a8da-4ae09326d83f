{"name": "telejson", "version": "7.2.0", "description": "", "keywords": ["JSON", "cyclic", "cyclical", "date", "function", "parse", "regex", "stringify"], "homepage": "https://github.com/storybookjs/telejson", "bugs": {"url": "https://github.com/storybookjs/telejson/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/telejson.git"}, "license": "MIT", "author": "<EMAIL>", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts"], "scripts": {"build": "tsup", "lint": "eslint src --ext .js,.ts", "prepublish": "yarn build", "test": "yarn test-node && yarn test-browser", "test-browser": "TZ=UTC jest --env=jsdom ./common ./browser", "test-node": "TZ=UTC jest --env=node ./common ./node", "release": "yarn build && auto shipit"}, "eslintConfig": {"env": {"browser": true}, "parserOptions": {"project": "./tsconfig.json"}, "extends": ["@storybook/eslint-config-storybook"]}, "jest": {}, "dependencies": {"memoizerific": "^1.11.3"}, "devDependencies": {"@auto-it/released": "^11.0.1", "@babel/preset-env": "^7.20.0", "@babel/preset-typescript": "^7.18.6", "@storybook/eslint-config-storybook": "^3.1.2", "@types/is-function": "^1.0.0", "@types/jest": "^26.0.22", "@types/lodash-es": "^4.17.6", "auto": "^11.0.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "^26.6.3", "common-tags": "^1.8.0", "eslint": "^7.23.0", "global": "^4.4.0", "is-function": "^1.0.2", "is-regex": "^1.1.2", "is-symbol": "^1.0.3", "isobject": "^4.0.0", "jest": "^26.6.3", "lodash-es": "^4.17.21", "prettier": "^2.2.1", "tsup": "^6.2.2", "typescript": "^4.2.3"}, "publishConfig": {"access": "public"}, "auto": {"plugins": ["npm", "released"]}}