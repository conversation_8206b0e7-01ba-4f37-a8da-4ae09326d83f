{"mappings": ";;;;;;;;ACAA;;AAIA,MAAMG,gCAAU,GAAIF,YAAD,CAAe,OAAA,CAAQG,QAAR,EAAf,CAAA,IAAuC,CAAA,IAAMC,SAA7C;AAAA,CAAA,AAAnB,AAAA;AACA,IAAIC,2BAAK,GAAG,CAAZ,AAAA;AAEA,SAASN,yCAAT,CAAeO,eAAf,EAAiD;IAC/C,MAAM,CAACC,EAAD,EAAKC,KAAL,CAAA,GAAcR,YAAK,CAACS,QAAN,CAAmCP,gCAAU,EAA7C,CAApB,AAD+C,EAE/C,+DADA;IAEAD,kDAAe,CAAC,IAAM;QACpB,IAAI,CAACK,eAAL,EAAsBE,KAAK,CAAEE,CAAAA,OAAD,GAAaA,OAAb,KAAA,IAAA,IAAaA,OAAb,KAAA,KAAA,CAAA,GAAaA,OAAb,GAAwBC,MAAM,CAACN,2BAAK,EAAN,CAA/B;QAAA,CAAL,CAAtB;KADa,EAEZ;QAACC,eAAD;KAFY,CAAf,CAEC;IACD,OAAOA,eAAe,IAAKC,CAAAA,EAAE,GAAI,CAAA,MAAA,EAAQA,EAAG,CAAA,CAAf,GAAmB,EAA1B,CAAA,AAAtB,CAAA;CACD;;ADdD", "sources": ["packages/react/id/src/index.ts", "packages/react/id/src/id.tsx"], "sourcesContent": ["export { useId } from './id';\n", "import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// We `toString()` to prevent bundlers from trying to `import { useId } from 'react';`\nconst useReactId = (React as any)['useId'.toString()] || (() => undefined);\nlet count = 0;\n\nfunction useId(deterministicId?: string): string {\n  const [id, setId] = React.useState<string | undefined>(useReactId());\n  // React versions older than 18 will have client-side ids only.\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : '');\n}\n\nexport { useId };\n"], "names": ["useId", "React", "useLayoutEffect", "useReactId", "toString", "undefined", "count", "deterministicId", "id", "setId", "useState", "reactId", "String"], "version": 3, "file": "index.js.map"}