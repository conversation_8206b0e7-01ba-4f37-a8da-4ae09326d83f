{"mappings": ";;;;;;;;;;;;;;A;;;;ACMA;;oGAEA,CAEA,MAAMK,iCAAW,GAAG,QAApB,AAAA;AAQA,MAAML,yCAAM,GAAA,aAAGE,CAAAA,uBAAA,CAA6C,CAACK,KAAD,EAAQC,YAAR,GAAyB;IAAA,IAAA,oBAAA,AAAA;IACnF,MAAM,aAAEC,SAAS,GAAGC,UAAH,KAAA,IAAA,IAAGA,UAAH,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,AAAA,CAAA,oBAAA,GAAGA,UAAU,CAAEC,QAAf,CAAA,KAAA,IAAA,IAAA,oBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAG,oBAAA,CAAsBC,IAApC,GAA0C,GAAGC,WAAH,EAA1C,GAA6DN,KAAnE,AAAM;IACN,OAAOE,SAAS,GAAA,aACZN,CAAAA,yCAAQ,CAACW,YAAT,CAAA,aAAsB,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EAAA,2DAAA,CAAA,EAAA,EAAmBD,WAAnB,EAD1B;QAC0D,GAAG,EAAEL,YAAL;KAAhC,CAAA,CAAtB,EAA6EC,SAA7E,CADY,GAEZ,IAFJ,CAC0B;CAHb,CAAf,AAKC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAEA,MAAMR,yCAAI,GAAGD,yCAAb,AAAA;;AD7BA", "sources": ["packages/react/portal/src/index.ts", "packages/react/portal/src/Portal.tsx"], "sourcesContent": ["export {\n  Portal,\n  //\n  Root,\n} from './Portal';\nexport type { PortalProps } from './Portal';\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  container?: HTMLElement | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container = globalThis?.document?.body, ...portalProps } = props;\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n"], "names": ["Portal", "Root", "React", "ReactDOM", "Primitive", "PORTAL_NAME", "forwardRef", "props", "forwardedRef", "container", "globalThis", "document", "body", "portalProps", "createPortal"], "version": 3, "file": "index.js.map"}