{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;A;;;;;;;;;;;;;;;;;;;;;;;;;AC8BA,MAAMyD,+BAAS,GAAG;IAAC,GAAD;IAAM,OAAN;IAAe,SAAf;IAA0B,WAA1B;CAAlB,AAAA;AACA,MAAMC,oCAAc,GAAG;IAAC,GAAD;IAAM,OAAN;CAAvB,AAAA;AAEA;;oGAEA,CAEA,MAAMC,iCAAW,GAAG,QAApB,AAAA;AAGA,MAAM,CAACC,gCAAD,EAAaC,mCAAb,EAA4BC,2CAA5B,CAAA,GAAqDzB,8CAAgB,CAGzEsB,iCAHyE,CAA3E,AAAA;AAMA,MAAM,CAACI,yCAAD,EAAsB/D,yCAAtB,CAAA,GAA2CuC,6CAAkB,CAACoB,iCAAD,EAAc;IAC/EG,2CAD+E;IAE/EhB,2CAF+E;CAAd,CAAnE,AAAA;AAIA,MAAMkB,oCAAc,GAAGlB,2CAAiB,EAAxC,AAAA;AAoBA,MAAM,CAACmB,oCAAD,EAAiBC,sCAAjB,CAAA,GAAqCH,yCAAmB,CAAqBJ,iCAArB,CAA9D,AAAA;AAQA,MAAM,CAACQ,iDAAD,EAA8BC,mDAA9B,CAAA,GACJL,yCAAmB,CAAkCJ,iCAAlC,CADrB,AAAA;AAkBA,MAAM1D,yCAA6B,GAAIoE,CAAAA,KAAD,GAAqC;IACzE,MAAM,E,eACJC,aADI,CAAA,E,UAEJC,QAFI,CAAA,EAGJC,IAAI,EAAEC,QAHF,CAAA,E,aAIJC,WAJI,CAAA,E,cAKJC,YALI,CAAA,EAMJC,KAAK,EAAEC,SANH,CAAA,E,cAOJC,YAPI,CAAA,E,eAQJC,aARI,CAAA,E,KASJC,GATI,CAAA,E,MAUJC,IAVI,CAAA,E,cAWJC,YAXI,CAAA,E,UAYJC,QAZI,CAAA,E,UAaJC,QAAAA,CAAAA,EAbI,GAcFf,KAdJ,AAAM;IAeN,MAAMgB,WAAW,GAAGrB,oCAAc,CAACM,aAAD,CAAlC,AAAA;IACA,MAAM,CAACgB,OAAD,EAAUC,UAAV,CAAA,GAAwBtD,qBAAA,CAA4C,IAA5C,CAA9B,AAAA;IACA,MAAM,CAACwD,SAAD,EAAYC,YAAZ,CAAA,GAA4BzD,qBAAA,CAA0C,IAA1C,CAAlC,AAAA;IACA,MAAM,CAAC0D,oBAAD,EAAuBC,uBAAvB,CAAA,GAAkD3D,qBAAA,CAAe,KAAf,CAAxD,AAAA;IACA,MAAM4D,SAAS,GAAGrD,yCAAY,CAACwC,GAAD,CAA9B,AAAA;IACA,MAAM,CAACR,IAAI,GAAG,KAAR,EAAesB,OAAf,CAAA,GAA0B3C,4DAAoB,CAAC;QACnD4C,IAAI,EAAEtB,QAD6C;QAEnDuB,WAAW,EAAEtB,WAFsC;QAGnDuB,QAAQ,EAAEtB,YAAVsB;KAHkD,CAApD,AAAqD;IAKrD,MAAM,CAACrB,KAAD,EAAQsB,QAAR,CAAA,GAAoB/C,4DAAoB,CAAC;QAC7C4C,IAAI,EAAElB,SADuC;QAE7CmB,WAAW,EAAElB,YAFgC;QAG7CmB,QAAQ,EAAElB,aAAVkB;KAH4C,CAA9C,AAA+C;IAK/C,MAAME,wBAAwB,GAAGlE,mBAAA,CAA8C,IAA9C,CAAjC,AA/ByE,EAiCzE,iFAFA;IAGA,MAAMoE,aAAa,GAAGf,OAAO,GAAGgB,OAAO,CAAChB,OAAO,CAACiB,OAAR,CAAgB,MAAhB,CAAD,CAAV,GAAsC,IAAnE,AAAA;IACA,MAAM,CAACC,gBAAD,EAAmBC,mBAAnB,CAAA,GAA0CxE,qBAAA,CAAe,IAAIyE,GAAJ,EAAf,CAAhD,AAnCyE,EAqCzE,qFAFA;IAGA,kEAAA;IACA,uFAAA;IACA,4FAAA;IACA,gCAAA;IACA,MAAMC,eAAe,GAAGC,KAAK,CAACC,IAAN,CAAWL,gBAAX,CAAA,CACrBM,GADqB,CAChBC,CAAAA,MAAD,GAAYA,MAAM,CAAC1C,KAAP,CAAaO,KADR;IAAA,CAAA,CAErBoC,IAFqB,CAEhB,GAFgB,CAAxB,AAAA;IAIA,OAAA,aACE,CAAA,0BAAA,CAAC,8BAAD,EAA0B3B,WAA1B,EAAA,aACE,CAAA,0BAAA,CAAC,oCAAD,EAFJ;QAGM,QAAQ,EAAED,QADZ;QAEE,KAAK,EAAEd,aAFT;QAGE,OAAO,EAAEgB,OAHX;QAIE,eAAe,EAAEC,UAJnB;QAKE,SAAS,EAAEE,SALb;QAME,iBAAiB,EAAEC,YANrB;QAOE,oBAAoB,EAAEC,oBAPxB;QAQE,4BAA4B,EAAEC,uBARhC;QASE,SAAS,EAAEhD,2BAAK,EATlB;QAUE,KAAK,EAAEgC,KAVT;QAWE,aAAa,EAAEsB,QAXjB;QAYE,IAAI,EAAE1B,IAZR;QAaE,YAAY,EAAEsB,OAbhB;QAcE,GAAG,EAAED,SAdP;QAeE,wBAAwB,EAAEM,wBAf5B;QAgBE,QAAQ,EAAEhB,QAAV;KAhBF,EAAA,aAkBE,CAAA,0BAAA,CAAC,gCAAD,CAAY,QAAZ,EAlBF;QAkBuB,KAAK,EAAEb,aAAP;KAArB,EAAA,aACE,CAAA,0BAAA,CAAC,iDAAD,EADF;QAEI,KAAK,EAAED,KAAK,CAACC,aADf;QAEE,iBAAiB,EAAErC,wBAAA,CAAmB8E,CAAAA,MAAD,GAAY;YAC/CN,mBAAmB,CAAES,CAAAA,IAAD,GAAU,IAAIR,GAAJ,CAAQQ,IAAR,CAAA,CAAcC,GAAd,CAAkBJ,MAAlB,CAAX;YAAA,CAAnB,CAAAN;SADiB,EAEhB,EAFgB,CAFrB;QAKE,oBAAoB,EAAExE,wBAAA,CAAmB8E,CAAAA,MAAD,GAAY;YAClDN,mBAAmB,CAAES,CAAAA,IAAD,GAAU;gBAC5B,MAAME,UAAU,GAAG,IAAIV,GAAJ,CAAQQ,IAAR,CAAnB,AAAA;gBACAE,UAAU,CAACC,MAAX,CAAkBN,MAAlB,CAAAK,CAAAA;gBACA,OAAOA,UAAP,CAAA;aAHiB,CAAnB,CAIC;SALmB,EAMnB,EANmB,CAMrB;KAXH,EAaG7C,QAbH,CADF,CAlBF,EAoCG8B,aAAa,GAAA,aACZ,CAAA,0BAAA,CAAC,kCAAD,EAlBA;QAmBE,GAAG,EAAEM,eADP;QAEE,aAAA,EAAA,IAFF;QAGE,QAAQ,EAAEvB,QAHZ;QAIE,QAAQ,EAAE,EAJZ;QAKE,IAAI,EAAEH,IALR;QAME,YAAY,EAAEC,YANhB;QAOE,KAAK,EAAEN,KAPT,CAQE,uBADA;QAPF;QASE,QAAQ,EAAG0C,CAAAA,KAAD,GAAWpB,QAAQ,CAACoB,KAAK,CAACC,MAAN,CAAa3C,KAAd,CAT/B;QAAA;QAUE,QAAQ,EAAEO,QAAV;KAVF,EAYGP,KAAK,KAAK4C,SAAV,GAAA,aAAsB,CAAA,0BAZzB,CAAA,QAAA,EAAA;QAYiC,KAAK,EAAC,EAAN;KAAR,CAAtB,GAA4C,IAZ/C,EAaGZ,KAAK,CAACC,IAAN,CAAWL,gBAAX,CAbH,CADY,GAgBV,IApDN,CADF,CADF,CAmDiC;CAjGnC,AAwGC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMiB,kCAAY,GAAG,eAArB,AAAA;AAMA,MAAMvH,wCAAa,GAAA,aAAG+B,CAAAA,uBAAA,CACpB,CAACoC,KAAD,EAAyCsD,YAAzC,GAA0D;IACxD,MAAM,E,eAAErD,aAAF,CAAA,YAAiBa,QAAQ,GAAG,KAA5B,GAAmC,GAAGyC,YAAH,EAAnC,GAAuDvD,KAA7D,AAAM;IACN,MAAMgB,WAAW,GAAGrB,oCAAc,CAACM,aAAD,CAAlC,AAAA;IACA,MAAMuD,OAAO,GAAG3D,sCAAgB,CAACuD,kCAAD,EAAenD,aAAf,CAAhC,AAAA;IACA,MAAMwD,UAAU,GAAGD,OAAO,CAAC1C,QAAR,IAAoBA,QAAvC,AAAA;IACA,MAAM4C,YAAY,GAAGzF,8CAAe,CAACqF,YAAD,EAAeE,OAAO,CAACG,eAAvB,CAApC,AAAA;IACA,MAAMC,QAAQ,GAAGpE,mCAAa,CAACS,aAAD,CAA9B,AAAA;IAEA,MAAM,CAAC4D,SAAD,EAAYC,qBAAZ,EAAmCC,cAAnC,CAAA,GAAqDC,wCAAkB,CAAEC,CAAAA,MAAD,GAAY;QACxF,MAAMC,YAAY,GAAGN,QAAQ,EAAA,CAAGO,MAAX,CAAmBC,CAAAA,IAAD,GAAU,CAACA,IAAI,CAACtD,QAAlC;QAAA,CAArB,AAAA;QACA,MAAMuD,WAAW,GAAGH,YAAY,CAACI,IAAb,CAAmBF,CAAAA,IAAD,GAAUA,IAAI,CAAC7D,KAAL,KAAeiD,OAAO,CAACjD,KAAnD;QAAA,CAApB,AAAA;QACA,MAAMgE,QAAQ,GAAGC,kCAAY,CAACN,YAAD,EAAeD,MAAf,EAAuBI,WAAvB,CAA7B,AAAA;QACA,IAAIE,QAAQ,KAAKpB,SAAjB,EACEK,OAAO,CAAC9C,aAAR,CAAsB6D,QAAQ,CAAChE,KAA/B,CAAAiD,CAAAA;KALyE,CAA7E,AAOC;IAED,MAAMiB,UAAU,GAAG,IAAM;QACvB,IAAI,CAAChB,UAAL,EAAiB;YACfD,OAAO,CAAClD,YAAR,CAAqB,IAArB,CAAA,CADe,CAEf,+BADAkD;YAEAO,cAAc,EAAdA,CAAAA;SACD;KALH,AAMC;IAED,OAAA,aACE,CAAA,0BAAA,CAAC,gCAAD,EADF,2DAAA,CAAA;QAC0B,OAAO,EAAP,IAAA;KAAxB,EAAoC/C,WAApC,CAAA,EAAA,aACE,CAAA,0BAAA,CAAC,sCAAD,CAAW,MAAX,EADF,2DAAA,CAAA;QAEI,IAAI,EAAC,QADP;QAEE,IAAI,EAAC,UAFP;QAGE,eAAA,EAAewC,OAAO,CAACkB,SAHzB;QAIE,eAAA,EAAelB,OAAO,CAACrD,IAJzB;QAKE,eAAA,EAAeqD,OAAO,CAACzC,QALzB;QAME,mBAAA,EAAkB,MANpB;QAOE,GAAG,EAAEyC,OAAO,CAAC7C,GAPf;QAQE,YAAA,EAAY6C,OAAO,CAACrD,IAAR,GAAe,MAAf,GAAwB,QARtC;QASE,QAAQ,EAAEsD,UATZ;QAUE,eAAA,EAAeA,UAAU,GAAG,EAAH,GAAQN,SAVnC;QAWE,kBAAA,EAAkBK,OAAO,CAACjD,KAAR,KAAkB4C,SAAlB,GAA8B,EAA9B,GAAmCA,SAArD;KAXF,EAYMI,YAZN,EAAA;QAaE,GAAG,EAAEG,YAbP,CAcE,+EADA;QAbF;QAeE,OAAO,EAAE3F,4CAAoB,CAACwF,YAAY,CAACoB,OAAd,EAAwB1B,CAAAA,KAAD,GAAW;YAC7D,6EAAA;YACA,gFAAA;YACA,iFAAA;YACA,sEAAA;YACA,qCAAA;YACAA,KAAK,CAAC2B,aAAN,CAAoBC,KAApB,EAAA5B,CAAAA;SAN2B,CAf/B;QAuBE,aAAa,EAAElF,4CAAoB,CAACwF,YAAY,CAACuB,aAAd,EAA8B7B,CAAAA,KAAD,GAAW;YACzE,mCAAA;YACA,iEAAA;YACA,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB,AAAA;YACA,IAAIA,MAAM,CAAC6B,iBAAP,CAAyB9B,KAAK,CAAC+B,SAA/B,CAAJ,EACE9B,MAAM,CAAC+B,qBAAP,CAA6BhC,KAAK,CAAC+B,SAAnC,CAAA9B,CAAAA;YALuE,CAQzE,4FAFC;YAGD,uEAAA;YACA,IAAID,KAAK,CAACiC,MAAN,KAAiB,CAAjB,IAAsBjC,KAAK,CAACkC,OAAN,KAAkB,KAA5C,EAAmD;gBACjDV,UAAU,EAAVA,CAAAA;gBACAjB,OAAO,CAAC1B,wBAAR,CAAiCsD,OAAjC,GAA2C;oBACzCC,CAAC,EAAEC,IAAI,CAACC,KAAL,CAAWtC,KAAK,CAACuC,KAAjB,CADsC;oBAEzCC,CAAC,EAAEH,IAAI,CAACC,KAAL,CAAWtC,KAAK,CAACyC,KAAjB,CAAHD;iBAFF,CAFiD,CAMjD,0EAJ2C;gBAK3CxC,KAAK,CAAC0C,cAAN,EAAA1C,CAAAA;aACD;SAlBgC,CAvBrC;QA2CE,SAAS,EAAElF,4CAAoB,CAACwF,YAAY,CAACqC,SAAd,EAA0B3C,CAAAA,KAAD,GAAW;YACjE,MAAM4C,aAAa,GAAGhC,SAAS,CAACuB,OAAV,KAAsB,EAA5C,AAAA;YACA,MAAMU,aAAa,GAAG7C,KAAK,CAACkC,OAAN,IAAiBlC,KAAK,CAAC8C,MAAvB,IAAiC9C,KAAK,CAAC+C,OAA7D,AAAA;YACA,IAAI,CAACF,aAAD,IAAkB7C,KAAK,CAACgD,GAAN,CAAUC,MAAV,KAAqB,CAA3C,EAA8CpC,qBAAqB,CAACb,KAAK,CAACgD,GAAP,CAArB,CAA9C;YACA,IAAIJ,aAAa,IAAI5C,KAAK,CAACgD,GAAN,KAAc,GAAnC,EAAwC,OAAxC;YACA,IAAI7G,+BAAS,CAAC+G,QAAV,CAAmBlD,KAAK,CAACgD,GAAzB,CAAJ,EAAmC;gBACjCxB,UAAU,EAAVA,CAAAA;gBACAxB,KAAK,CAAC0C,cAAN,EAAA1C,CAAAA;aACD;SAR4B,CAS9B;KApDH,CAAA,CADF,CADF,CAEI;CA5Bc,CAAtB,AAoFG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,wCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMmD,gCAAU,GAAG,aAAnB,AAAA;AAQA,MAAMtK,yCAAW,GAAA,aAAG8B,CAAAA,uBAAA,CAClB,CAACoC,KAAD,EAAuCsD,YAAvC,GAAwD;IACtD,sEAAA;IACA,MAAM,E,eAAErD,aAAF,CAAA,E,WAAiBoG,SAAjB,CAAA,E,OAA4BC,KAA5B,CAAA,E,UAAmCpG,QAAnC,CAAA,E,aAA6CqG,WAA7C,CAAA,EAA0D,GAAGC,UAAH,EAA1D,GAA4ExG,KAAlF,AAAM;IACN,MAAMwD,OAAO,GAAG3D,sCAAgB,CAACuG,gCAAD,EAAanG,aAAb,CAAhC,AAAA;IACA,MAAM,E,8BAAEwG,4BAAAA,CAAAA,EAAF,GAAmCjD,OAAzC,AAAM;IACN,MAAMkD,WAAW,GAAGxG,QAAQ,KAAKiD,SAAjC,AAAA;IACA,MAAMO,YAAY,GAAGzF,8CAAe,CAACqF,YAAD,EAAeE,OAAO,CAACmD,iBAAvB,CAApC,AAAA;IAEA5H,kDAAe,CAAC,IAAM;QACpB0H,4BAA4B,CAACC,WAAD,CAA5B,CAAAD;KADa,EAEZ;QAACA,4BAAD;QAA+BC,WAA/B;KAFY,CAAf,CAEC;IAED,OAAA,aACE,CAAA,0BAAA,CAAC,sCAAD,CAAW,IAAX,EAAA,2DAAA,CAAA,EAAA,EACMF,UADN,EADF;QAGI,GAAG,EAAE9C,YAFP,CAGE,2EADA;QAFF;QAKE,KAAK,EAAE;YAAEkD,aAAa,EAAE,MAAfA;SAAF;KALT,CAAA,EAOGpD,OAAO,CAACjD,KAAR,KAAkB4C,SAAlB,IAA+BoD,WAAW,KAAKpD,SAA/C,GAA2DoD,WAA3D,GAAyErG,QAP5E,CADF,CACE;CAdc,CAApB,AAwBG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,gCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAM2G,+BAAS,GAAG,YAAlB,AAAA;AAKA,MAAM9K,yCAAU,GAAA,aAAG6B,CAAAA,uBAAA,CACjB,CAACoC,KAAD,EAAsCsD,YAAtC,GAAuD;IACrD,MAAM,E,eAAErD,aAAF,CAAA,E,UAAiBC,QAAjB,CAAA,EAA2B,GAAG4G,SAAH,EAA3B,GAA4C9G,KAAlD,AAAM;IACN,OAAA,aACE,CAAA,0BAAA,CAAC,sCAAD,CAAW,IAAX,EADF,2DAAA,CAAA;QACkB,aAAA,EAAA,IAAA;KAAhB,EAAgC8G,SAAhC,EAAA;QAA2C,GAAG,EAAExD,YAAL;KAA3C,CAAA,EACGpD,QAAQ,IAAI,KADf,CADF,CACE;CAJa,CAAnB,AAQG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,+BAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAM6G,iCAAW,GAAG,cAApB,AAAA;AAOA,MAAM/K,yCAAyC,GAAIgE,CAAAA,KAAD,GAA2C;IAC3F,OAAA,aAAO,CAAA,0BAAA,CAAC,gCAAD,EAAP,2DAAA,CAAA;QAAwB,OAAO,EAAP,IAAA;KAAjB,EAA6BA,KAA7B,CAAA,CAAP,CAAO;CADT,AAEC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMgH,kCAAY,GAAG,eAArB,AAAA;AAKA,MAAM/K,yCAAa,GAAA,aAAG2B,CAAAA,uBAAA,CACpB,CAACoC,KAAD,EAAyCsD,YAAzC,GAA0D;IACxD,MAAME,OAAO,GAAG3D,sCAAgB,CAACmH,kCAAD,EAAehH,KAAK,CAACC,aAArB,CAAhC,AAAA;IACA,MAAM,CAACgH,QAAD,EAAWC,WAAX,CAAA,GAA0BtJ,qBAAA,EAAhC,AAFwD,EAIxD,8FAFA;IAGAmB,kDAAe,CAAC,IAAM;QACpBmI,WAAW,CAAC,IAAIC,gBAAJ,EAAD,CAAX,CAAAD;KADa,EAEZ,EAFY,CAAf,CAEC;IAED,IAAI,CAAC1D,OAAO,CAACrD,IAAb,EAAmB;QACjB,MAAMiH,IAAI,GAAGH,QAAb,AAAA;QACA,OAAOG,IAAI,GAAA,aACPvJ,CAAAA,4BAAA,CAAA,aACE,CAAA,0BAAA,CAAC,2CAAD,EAFN;YAE6B,KAAK,EAAEmC,KAAK,CAACC,aAAb;SAAvB,EAAA,aACE,CAAA,0BAAA,CAAC,gCAAD,CAAY,IAAZ,EADF;YACmB,KAAK,EAAED,KAAK,CAACC,aAAb;SAAjB,EAAA,aACE,CAAA,0BAAA,CAAA,KAAA,EAAA,IAAA,EAAMD,KAAK,CAACE,QAAZ,CADF,CADF,CADF,EAMEkH,IANF,CADO,GASP,IATJ,CAGQ;KAOT;IAED,OAAA,aAAO,CAAA,0BAAA,CAAC,uCAAD,EAAA,2DAAA,CAAA,EAAA,EAAuBpH,KAAvB,EAAP;QAAqC,GAAG,EAAEsD,YAAL;KAA9B,CAAA,CAAP,CAAO;CAxBW,CAAtB,AAyBG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMgE,oCAAc,GAAG,EAAvB,AAAA;AAqBA,MAAM,CAACC,2CAAD,EAAwBC,6CAAxB,CAAA,GACJ9H,yCAAmB,CAA4BsH,kCAA5B,CADrB,AAAA;AAGA,MAAMS,uCAAiB,GAAG,mBAA1B,AAAA;AA8BA,MAAMC,uCAAiB,GAAA,aAAG9J,CAAAA,uBAAA,CACxB,CAACoC,KAAD,EAA6CsD,YAA7C,GAA8D;IAC5D,MAAM,E,eACJrD,aADI,CAAA,YAEJ0H,QAAQ,GAAG,cAFP,G,kBAGJC,gBAHI,CAAA,E,iBAIJC,eAJI,CAAA,E,sBAKJC,oBALI,CAAA,E,MAMJ,EAAA;IACA,sBAAA;IACAC,IARI,CAAA,E,YASJC,UATI,CAAA,E,OAUJC,KAVI,CAAA,E,aAWJC,WAXI,CAAA,E,cAYJC,YAZI,CAAA,E,mBAaJC,iBAbI,CAAA,E,kBAcJC,gBAdI,CAAA,E,QAeJC,MAfI,CAAA,E,kBAgBJC,gBAhBI,CAAA,E,iBAiBJC,eAjBI,CAAA,EAkBJ,EAAA;IACA,GAAGC,YAAH,EAnBI,GAoBFzI,KApBJ,AAAM;IAqBN,MAAMwD,OAAO,GAAG3D,sCAAgB,CAACmH,kCAAD,EAAe/G,aAAf,CAAhC,AAAA;IACA,MAAM,CAACyI,OAAD,EAAUC,UAAV,CAAA,GAAwB/K,qBAAA,CAAgD,IAAhD,CAA9B,AAAA;IACA,MAAM,CAACgL,QAAD,EAAWC,WAAX,CAAA,GAA0BjL,qBAAA,CAA6C,IAA7C,CAAhC,AAAA;IACA,MAAM8F,YAAY,GAAGzF,8CAAe,CAACqF,YAAD,EAAgBwF,CAAAA,IAAD,GAAUH,UAAU,CAACG,IAAD,CAAnC;IAAA,CAApC,AAAA;IACA,MAAM,CAACC,YAAD,EAAeC,eAAf,CAAA,GAAkCpL,qBAAA,CAAyC,IAAzC,CAAxC,AAAA;IACA,MAAM,CAACqL,gBAAD,EAAmBC,mBAAnB,CAAA,GAA0CtL,qBAAA,CAC9C,IAD8C,CAAhD,AAAA;IAGA,MAAMgG,QAAQ,GAAGpE,mCAAa,CAACS,aAAD,CAA9B,AAAA;IACA,MAAM,CAACkJ,YAAD,EAAeC,eAAf,CAAA,GAAkCxL,qBAAA,CAAe,KAAf,CAAxC,AAAA;IACA,MAAMyL,sBAAsB,GAAGzL,mBAAA,CAAa,KAAb,CAA/B,AAhC4D,EAkC5D,8FAFA;IAGAA,sBAAA,CAAgB,IAAM;QACpB,IAAI8K,OAAJ,EAAa,OAAOxJ,4BAAU,CAACwJ,OAAD,CAAjB,CAAb;KADF,EAEG;QAACA,OAAD;KAFH,CAAA,CAnC4D,CAuC5D,mEAFC;IAGD,wDAAA;IACArK,6CAAc,EAAdA,CAAAA;IAEA,MAAMkL,UAAU,GAAG3L,wBAAA,CAChB4L,CAAAA,UAAD,GAA2C;QACzC,MAAM,CAACC,SAAD,EAAY,GAAGC,SAAf,CAAA,GAA4B9F,QAAQ,EAAA,CAAGnB,GAAX,CAAgB2B,CAAAA,IAAD,GAAUA,IAAI,CAACuF,GAAL,CAASvE,OAAlC;QAAA,CAAlC,AAAA;QACA,MAAM,CAACwE,QAAD,CAAA,GAAaF,SAAS,CAACG,KAAV,CAAgB,EAAhB,CAAnB,AAAA;QAEA,MAAMC,0BAA0B,GAAGC,QAAQ,CAACC,aAA5C,AAAA;QACA,KAAK,MAAMC,SAAX,IAAwBT,UAAxB,CAAoC;YAClC,8FAAA;YACA,IAAIS,SAAS,KAAKH,0BAAlB,EAA8C,OAA9C;YACAG,SAAS,KAAA,IAAT,IAAAA,SAAS,KAAA,KAAA,CAAT,IAAAA,SAAS,CAAEC,cAAX,CAA0B;gBAAEC,KAAK,EAAE,SAAPA;aAA5B,CAAA,CAHkC,CAIlC,qFAD0B;YAE1B,IAAIF,SAAS,KAAKR,SAAd,IAA2Bb,QAA/B,EAAyCA,QAAQ,CAACwB,SAAT,GAAqB,CAArB,CAAzC;YACA,IAAIH,SAAS,KAAKL,QAAd,IAA0BhB,QAA9B,EAAwCA,QAAQ,CAACwB,SAAT,GAAqBxB,QAAQ,CAACyB,YAA9B,CAAxC;YACAJ,SAAS,KAAA,IAAT,IAAAA,SAAS,KAAA,KAAA,CAAT,IAAAA,SAAS,CAAEpF,KAAX,EAAAoF,CAAAA;YACA,IAAIF,QAAQ,CAACC,aAAT,KAA2BF,0BAA/B,EAA2D,OAA3D;SACD;KAfc,EAiBjB;QAAClG,QAAD;QAAWgF,QAAX;KAjBiB,CAAnB,AAgBG;IAIH,MAAM0B,iBAAiB,GAAG1M,wBAAA,CACxB,IAAM2L,UAAU,CAAC;YAACR,YAAD;YAAeL,OAAf;SAAD,CADQ;IAAA,EAExB;QAACa,UAAD;QAAaR,YAAb;QAA2BL,OAA3B;KAFwB,CAA1B,AA/D4D,EAoE5D,yFALA;IAMA,kGAAA;IACA9K,sBAAA,CAAgB,IAAM;QACpB,IAAIuL,YAAJ,EACEmB,iBAAiB,EAAjBA,CAAAA;KAFJ,EAIG;QAACnB,YAAD;QAAemB,iBAAf;KAJH,CAAA,CAtE4D,CA4E5D,wFAFC;IAGD,oCAAA;IACA,MAAM,E,cAAEhK,YAAF,CAAA,E,0BAAgBwB,wBAAAA,CAAAA,EAAhB,GAA6C0B,OAAnD,AAAM;IACN5F,sBAAA,CAAgB,IAAM;QACpB,IAAI8K,OAAJ,EAAa;YACX,IAAI6B,gBAAgB,GAAG;gBAAElF,CAAC,EAAE,CAAL;gBAAQI,CAAC,EAAE,CAAHA;aAA/B,AAAuB;YAEvB,MAAM+E,iBAAiB,GAAIvH,CAAAA,KAAD,GAAyB;gBAAA,IAAA,qBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,sBAAA,AAAA;gBACjDsH,gBAAgB,GAAG;oBACjBlF,CAAC,EAAEC,IAAI,CAACmF,GAAL,CAASnF,IAAI,CAACC,KAAL,CAAWtC,KAAK,CAACuC,KAAjB,CAAA,GAAA,CAAA,AAAA,CAAA,qBAAA,GAAA,AAAA,CAAA,sBAAA,GAA2B1D,wBAAwB,CAACsD,OAApD,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA2B,sBAAA,CAAkCC,CAA7D,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,qBAAA,GAAkE,CAAlE,CAAA,AAAT,CADc;oBAEjBI,CAAC,EAAEH,IAAI,CAACmF,GAAL,CAASnF,IAAI,CAACC,KAAL,CAAWtC,KAAK,CAACyC,KAAjB,CAAA,GAAA,CAAA,AAAA,CAAA,sBAAA,GAAA,AAAA,CAAA,sBAAA,GAA2B5D,wBAAwB,CAACsD,OAApD,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA2B,sBAAA,CAAkCK,CAA7D,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,sBAAA,GAAkE,CAAlE,CAAA,AAAT,CAAHA;iBAFF,CAAmB;aADrB,AAKC;YACD,MAAMiF,eAAe,GAAIzH,CAAAA,KAAD,GAAyB;gBAC/C,oGAAA;gBACA,IAAIsH,gBAAgB,CAAClF,CAAjB,IAAsB,EAAtB,IAA4BkF,gBAAgB,CAAC9E,CAAjB,IAAsB,EAAtD,EACExC,KAAK,CAAC0C,cAAN,EAAA1C,CAAAA;qBAEA,0DAAA;gBACA,IAAI,CAACyF,OAAO,CAACiC,QAAR,CAAiB1H,KAAK,CAACC,MAAvB,CAAL,EACE5C,YAAY,CAAC,KAAD,CAAZ,CAAAA;gBAGJyJ,QAAQ,CAACa,mBAAT,CAA6B,aAA7B,EAA4CJ,iBAA5C,CAAAT,CAAAA;gBACAjI,wBAAwB,CAACsD,OAAzB,GAAmC,IAAnC,CAAAtD;aAXF,AAYC;YAED,IAAIA,wBAAwB,CAACsD,OAAzB,KAAqC,IAAzC,EAA+C;gBAC7C2E,QAAQ,CAACc,gBAAT,CAA0B,aAA1B,EAAyCL,iBAAzC,CAAAT,CAAAA;gBACAA,QAAQ,CAACc,gBAAT,CAA0B,WAA1B,EAAuCH,eAAvC,EAAwD;oBAAEI,OAAO,EAAE,IAAX;oBAAiBC,IAAI,EAAE,IAANA;iBAAzE,CAAwD,CAAA;aACzD;YAED,OAAO,IAAM;gBACXhB,QAAQ,CAACa,mBAAT,CAA6B,aAA7B,EAA4CJ,iBAA5C,CAAAT,CAAAA;gBACAA,QAAQ,CAACa,mBAAT,CAA6B,WAA7B,EAA0CF,eAA1C,EAA2D;oBAAEI,OAAO,EAAE,IAATA;iBAA7D,CAA2D,CAAA;aAF7D,CAGC;SACF;KAjCH,EAkCG;QAACpC,OAAD;QAAUpI,YAAV;QAAwBwB,wBAAxB;KAlCH,CAkCC,CAAA;IAEDlE,sBAAA,CAAgB,IAAM;QACpB,MAAMoN,KAAK,GAAG,IAAM1K,YAAY,CAAC,KAAD,CAAhC;QAAA;QACA2K,MAAM,CAACJ,gBAAP,CAAwB,MAAxB,EAAgCG,KAAhC,CAAAC,CAAAA;QACAA,MAAM,CAACJ,gBAAP,CAAwB,QAAxB,EAAkCG,KAAlC,CAAAC,CAAAA;QACA,OAAO,IAAM;YACXA,MAAM,CAACL,mBAAP,CAA2B,MAA3B,EAAmCI,KAAnC,CAAAC,CAAAA;YACAA,MAAM,CAACL,mBAAP,CAA2B,QAA3B,EAAqCI,KAArC,CAAAC,CAAAA;SAFF,CAGC;KAPH,EAQG;QAAC3K,YAAD;KARH,CAQC,CAAA;IAED,MAAM,CAACuD,SAAD,EAAYC,qBAAZ,CAAA,GAAqCE,wCAAkB,CAAEC,CAAAA,MAAD,GAAY;QACxE,MAAMC,YAAY,GAAGN,QAAQ,EAAA,CAAGO,MAAX,CAAmBC,CAAAA,IAAD,GAAU,CAACA,IAAI,CAACtD,QAAlC;QAAA,CAArB,AAAA;QACA,MAAMuD,WAAW,GAAGH,YAAY,CAACI,IAAb,CAAmBF,CAAAA,IAAD,GAAUA,IAAI,CAACuF,GAAL,CAASvE,OAAT,KAAqB2E,QAAQ,CAACC,aAA1D;QAAA,CAApB,AAAA;QACA,MAAMzF,QAAQ,GAAGC,kCAAY,CAACN,YAAD,EAAeD,MAAf,EAAuBI,WAAvB,CAA7B,AAAA;QACA,IAAIE,QAAJ,EACE;;;SAGR,CACQ2G,UAAU,CAAC,IAAO3G,QAAQ,CAACoF,GAAT,CAAavE,OAAd,CAAsCP,KAAtC,EAAP;QAAA,CAAV,CAAAqG;KATyD,CAA7D,AAWC;IAED,MAAMC,eAAe,GAAGvN,wBAAA,CACtB,CAACkL,IAAD,EAAiCvI,KAAjC,EAAgDO,QAAhD,GAAsE;QACpE,MAAMsK,gBAAgB,GAAG,CAAC/B,sBAAsB,CAACjE,OAAxB,IAAmC,CAACtE,QAA7D,AAAA;QACA,MAAMuK,cAAc,GAAG7H,OAAO,CAACjD,KAAR,KAAkB4C,SAAlB,IAA+BK,OAAO,CAACjD,KAAR,KAAkBA,KAAxE,AAAA;QACA,IAAI8K,cAAc,IAAID,gBAAtB,EAAwC;YACtCpC,eAAe,CAACF,IAAD,CAAf,CAAAE;YACA,IAAIoC,gBAAJ,EAAsB/B,sBAAsB,CAACjE,OAAvB,GAAiC,IAAjC,CAAtB;SACD;KAPmB,EAStB;QAAC5B,OAAO,CAACjD,KAAT;KATsB,CAAxB,AAQG;IAGH,MAAM+K,eAAe,GAAG1N,wBAAA,CAAkB,IAAM8K,OAAN,KAAA,IAAA,IAAMA,OAAN,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAMA,OAAO,CAAE7D,KAAT,EAAxB;IAAA,EAA0C;QAAC6D,OAAD;KAA1C,CAAxB,AAAA;IACA,MAAM6C,mBAAmB,GAAG3N,wBAAA,CAC1B,CAACkL,IAAD,EAAqCvI,KAArC,EAAoDO,QAApD,GAA0E;QACxE,MAAMsK,gBAAgB,GAAG,CAAC/B,sBAAsB,CAACjE,OAAxB,IAAmC,CAACtE,QAA7D,AAAA;QACA,MAAMuK,cAAc,GAAG7H,OAAO,CAACjD,KAAR,KAAkB4C,SAAlB,IAA+BK,OAAO,CAACjD,KAAR,KAAkBA,KAAxE,AAAA;QACA,IAAI8K,cAAc,IAAID,gBAAtB,EACElC,mBAAmB,CAACJ,IAAD,CAAnB,CAAAI;KALsB,EAQ1B;QAAC1F,OAAO,CAACjD,KAAT;KAR0B,CAA5B,AAOG;IAIH,MAAMiL,cAAc,GAAG7D,QAAQ,KAAK,QAAb,GAAwB8D,0CAAxB,GAA+CC,+CAAtE,AAjK4D,EAmK5D,8EAFA;IAGA,MAAMC,kBAAkB,GACtBH,cAAc,KAAKC,0CAAnB,GACI;Q,MACE1D,IADF;Q,YAEEC,UAFF;Q,OAGEC,KAHF;Q,aAIEC,WAJF;Q,cAKEC,YALF;Q,mBAMEC,iBANF;Q,kBAOEC,gBAPF;Q,QAQEC,MARF;Q,kBASEC,gBATF;Q,iBAUEC,eAAAA;KAXN,GAaI,EAdN,AAEM;IAcN,OAAA,aACE,CAAA,0BAAA,CAAC,2CAAD,EADF;QAEI,KAAK,EAAEvI,aADT;QAEE,OAAO,EAAEyI,OAFX;QAGE,QAAQ,EAAEE,QAHZ;QAIE,gBAAgB,EAAEC,WAJpB;QAKE,eAAe,EAAEsC,eALnB;QAME,YAAY,EAAEpC,YANhB;QAOE,WAAW,EAAEuC,eAPf;QAQE,mBAAmB,EAAEC,mBARvB;QASE,iBAAiB,EAAEjB,iBATrB;QAUE,gBAAgB,EAAErB,gBAVpB;QAWE,QAAQ,EAAEtB,QAXZ;QAYE,YAAY,EAAEwB,YAZhB;QAaE,SAAS,EAAEtF,SAAX;KAbF,EAAA,aAeE,CAAA,0BAAA,CAAC,qCAAD,EAfF;QAegB,EAAE,EAAEjF,4BAAlB;QAAwB,cAAc,EAAd,IAAA;KAAxB,EAAA,aACE,CAAA,0BAAA,CAAC,wCAAD,EADF;QAEI,OAAO,EAAA,IADT,CAEE,wDADA;QADF;QAIE,OAAO,EAAE4E,OAAO,CAACrD,IAJnB;QAKE,gBAAgB,EAAG8C,CAAAA,KAAD,GAAW;YAC3B,wEAAA;YACAA,KAAK,CAAC0C,cAAN,EAAA1C,CAAAA;SAPJ;QASE,kBAAkB,EAAElF,4CAAoB,CAAC6J,gBAAD,EAAoB3E,CAAAA,KAAD,GAAW;YAAA,IAAA,gBAAA,AAAA;YACpE,CAAA,gBAAA,GAAAO,OAAO,CAACvC,OAAR,CAAA,KAAA,IAAA,IAAA,gBAAA,KAAA,KAAA,CAAA,IAAA,gBAAA,CAAiB4D,KAAjB,CAAuB;gBAAE+G,aAAa,EAAE,IAAfA;aAAzB,CAAuB,CAAA;YACvB3I,KAAK,CAAC0C,cAAN,EAAA1C,CAAAA;SAFsC,CAGvC;KAZH,EAAA,aAcE,CAAA,0BAAA,CAAC,oDAAD,EAdF;QAeI,OAAO,EAAA,IADT;QAEE,2BAA2B,EAAA,IAF7B;QAGE,eAAe,EAAE4E,eAHnB;QAIE,oBAAoB,EAAEC,oBAJxB,CAKE,4DADA;QAJF;QAOE,cAAc,EAAG7E,CAAAA,KAAD,GAAWA,KAAK,CAAC0C,cAAN,EAP7B;QAAA;QAQE,SAAS,EAAE,IAAMnC,OAAO,CAAClD,YAAR,CAAqB,KAArB,CAAjB;KARF,EAAA,aAUE,CAAA,0BAAA,CAAC,cAAD,EAVF,2DAAA,CAAA;QAWI,IAAI,EAAC,SADP;QAEE,EAAE,EAAEkD,OAAO,CAACkB,SAFd;QAGE,YAAA,EAAYlB,OAAO,CAACrD,IAAR,GAAe,MAAf,GAAwB,QAHtC;QAIE,GAAG,EAAEqD,OAAO,CAAC7C,GAJf;QAKE,aAAa,EAAGsC,CAAAA,KAAD,GAAWA,KAAK,CAAC0C,cAAN,EAA1B;KALF,EAMM8C,YANN,EAOMkD,kBAPN,EAAA;QAQE,QAAQ,EAAE,IAAMvC,eAAe,CAAC,IAAD,CARjC;QAAA;QASE,GAAG,EAAE1F,YATP;QAUE,KAAK,EAAE;YACL,0DAAA;YACAmI,OAAO,EAAE,MAFJ;YAGLC,aAAa,EAAE,QAHV;YAIL,8DAAA;YACAC,OAAO,EAAE,MALJ;YAML,GAAGtD,YAAY,CAACnC,KAAhB;SAhBJ;QAkBE,SAAS,EAAEvI,4CAAoB,CAAC0K,YAAY,CAAC7C,SAAd,EAA0B3C,CAAAA,KAAD,GAAW;YACjE,MAAM6C,aAAa,GAAG7C,KAAK,CAACkC,OAAN,IAAiBlC,KAAK,CAAC8C,MAAvB,IAAiC9C,KAAK,CAAC+C,OAA7D,AADiE,EAGjE,gEAFA;YAGA,IAAI/C,KAAK,CAACgD,GAAN,KAAc,KAAlB,EAAyBhD,KAAK,CAAC0C,cAAN,EAAzB,CAAA;YAEA,IAAI,CAACG,aAAD,IAAkB7C,KAAK,CAACgD,GAAN,CAAUC,MAAV,KAAqB,CAA3C,EAA8CpC,qBAAqB,CAACb,KAAK,CAACgD,GAAP,CAArB,CAA9C;YAEA,IAAI;gBAAC,SAAD;gBAAY,WAAZ;gBAAyB,MAAzB;gBAAiC,KAAjC;aAAA,CAAwCE,QAAxC,CAAiDlD,KAAK,CAACgD,GAAvD,CAAJ,EAAiE;gBAC/D,MAAM+F,KAAK,GAAGpI,QAAQ,EAAA,CAAGO,MAAX,CAAmBC,CAAAA,IAAD,GAAU,CAACA,IAAI,CAACtD,QAAlC;gBAAA,CAAd,AAAA;gBACA,IAAImL,cAAc,GAAGD,KAAK,CAACvJ,GAAN,CAAW2B,CAAAA,IAAD,GAAUA,IAAI,CAACuF,GAAL,CAASvE,OAA7B;gBAAA,CAArB,AAAA;gBAEA,IAAI;oBAAC,SAAD;oBAAY,KAAZ;iBAAA,CAAmBe,QAAnB,CAA4BlD,KAAK,CAACgD,GAAlC,CAAJ,EACEgG,cAAc,GAAGA,cAAc,CAACpC,KAAf,EAAA,CAAuBqC,OAAvB,EAAjB,CAAAD;gBAEF,IAAI;oBAAC,SAAD;oBAAY,WAAZ;iBAAA,CAAyB9F,QAAzB,CAAkClD,KAAK,CAACgD,GAAxC,CAAJ,EAAkD;oBAChD,MAAMkG,cAAc,GAAGlJ,KAAK,CAACC,MAA7B,AAAA;oBACA,MAAMkJ,YAAY,GAAGH,cAAc,CAACI,OAAf,CAAuBF,cAAvB,CAArB,AAAA;oBACAF,cAAc,GAAGA,cAAc,CAACpC,KAAf,CAAqBuC,YAAY,GAAG,CAApC,CAAjB,CAAAH;iBACD;gBAED;;;WAGpB,CACoBf,UAAU,CAAC,IAAM3B,UAAU,CAAC0C,cAAD,CAAjB;gBAAA,CAAV,CAAAf;gBAEAjI,KAAK,CAAC0C,cAAN,EAAA1C,CAAAA;aACD;SA5B4B,CA6B9B;KA/CH,CAAA,CAVF,CAdF,CADF,CAfF,CADF,CAyCU;CA9NY,CAA1B,AAoRG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,uCAAA,EAAA;IAAA,WAAA,EAAA,uCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMqJ,gDAA0B,GAAG,2BAAnC,AAAA;AAKA,MAAMZ,+CAAyB,GAAA,aAAG9N,CAAAA,uBAAA,CAGhC,CAACoC,KAAD,EAAqDsD,YAArD,GAAsE;IACtE,MAAM,E,eAAErD,aAAF,CAAA,E,UAAiBsM,QAAjB,CAAA,EAA2B,GAAGC,WAAH,EAA3B,GAA8CxM,KAApD,AAAM;IACN,MAAMwD,OAAO,GAAG3D,sCAAgB,CAACmH,kCAAD,EAAe/G,aAAf,CAAhC,AAAA;IACA,MAAMwM,cAAc,GAAGjF,6CAAuB,CAACR,kCAAD,EAAe/G,aAAf,CAA9C,AAAA;IACA,MAAM,CAACyM,cAAD,EAAiBC,iBAAjB,CAAA,GAAsC/O,qBAAA,CAAsC,IAAtC,CAA5C,AAAA;IACA,MAAM,CAAC8K,OAAD,EAAUC,UAAV,CAAA,GAAwB/K,qBAAA,CAAwD,IAAxD,CAA9B,AAAA;IACA,MAAM8F,YAAY,GAAGzF,8CAAe,CAACqF,YAAD,EAAgBwF,CAAAA,IAAD,GAAUH,UAAU,CAACG,IAAD,CAAnC;IAAA,CAApC,AAAA;IACA,MAAMlF,QAAQ,GAAGpE,mCAAa,CAACS,aAAD,CAA9B,AAAA;IACA,MAAM2M,uBAAuB,GAAGhP,mBAAA,CAAa,KAAb,CAAhC,AAAA;IACA,MAAMiP,mBAAmB,GAAGjP,mBAAA,CAAa,IAAb,CAA5B,AAAA;IAEA,MAAM,E,UAAEgL,QAAF,CAAA,E,cAAYG,YAAZ,CAAA,E,kBAA0BE,gBAA1B,CAAA,E,mBAA4CqB,iBAAAA,CAAAA,EAA5C,GAAkEmC,cAAxE,AAAM;IACN,MAAM9E,QAAQ,GAAG/J,wBAAA,CAAkB,IAAM;QACvC,IACE4F,OAAO,CAACvC,OAAR,IACAuC,OAAO,CAACpC,SADR,IAEAsL,cAFA,IAGAhE,OAHA,IAIAE,QAJA,IAKAG,YALA,IAMAE,gBAPF,EAQE;YACA,MAAM6D,WAAW,GAAGtJ,OAAO,CAACvC,OAAR,CAAgB8L,qBAAhB,EAApB,AADA,EAGA,4FAFA;YAGA,0BAAA;YACA,4FAAA;YACA,MAAMC,WAAW,GAAGtE,OAAO,CAACqE,qBAAR,EAApB,AAAA;YACA,MAAME,aAAa,GAAGzJ,OAAO,CAACpC,SAAR,CAAkB2L,qBAAlB,EAAtB,AAAA;YACA,MAAMG,YAAY,GAAGjE,gBAAgB,CAAC8D,qBAAjB,EAArB,AAAA;YAEA,IAAIvJ,OAAO,CAAC7C,GAAR,KAAgB,KAApB,EAA2B;gBACzB,MAAMwM,cAAc,GAAGD,YAAY,CAACE,IAAb,GAAoBJ,WAAW,CAACI,IAAvD,AAAA;gBACA,MAAMA,IAAI,GAAGH,aAAa,CAACG,IAAd,GAAqBD,cAAlC,AAAA;gBACA,MAAME,SAAS,GAAGP,WAAW,CAACM,IAAZ,GAAmBA,IAArC,AAAA;gBACA,MAAME,eAAe,GAAGR,WAAW,CAACS,KAAZ,GAAoBF,SAA5C,AAAA;gBACA,MAAMG,YAAY,GAAGlI,IAAI,CAACmI,GAAL,CAASH,eAAT,EAA0BN,WAAW,CAACO,KAAtC,CAArB,AAAA;gBACA,MAAMG,SAAS,GAAGzC,MAAM,CAAC0C,UAAP,GAAoBrG,oCAAtC,AAAA;gBACA,MAAMsG,WAAW,GAAG9P,0BAAK,CAACsP,IAAD,EAAO;oBAAC9F,oCAAD;oBAAiBoG,SAAS,GAAGF,YAA7B;iBAAP,CAAzB,AAAA;gBAEAd,cAAc,CAACpG,KAAf,CAAqBuH,QAArB,GAAgCP,eAAe,GAAG,IAAlD,CAAAZ;gBACAA,cAAc,CAACpG,KAAf,CAAqB8G,IAArB,GAA4BQ,WAAW,GAAG,IAA1C,CAAAlB;aAVF,MAWO;gBACL,MAAMS,cAAc,GAAGH,WAAW,CAACc,KAAZ,GAAoBZ,YAAY,CAACY,KAAxD,AAAA;gBACA,MAAMA,KAAK,GAAG7C,MAAM,CAAC0C,UAAP,GAAoBV,aAAa,CAACa,KAAlC,GAA0CX,cAAxD,AAAA;gBACA,MAAMY,UAAU,GAAG9C,MAAM,CAAC0C,UAAP,GAAoBb,WAAW,CAACgB,KAAhC,GAAwCA,KAA3D,AAAA;gBACA,MAAMR,eAAe,GAAGR,WAAW,CAACS,KAAZ,GAAoBQ,UAA5C,AAAA;gBACA,MAAMP,YAAY,GAAGlI,IAAI,CAACmI,GAAL,CAASH,eAAT,EAA0BN,WAAW,CAACO,KAAtC,CAArB,AAAA;gBACA,MAAMS,QAAQ,GAAG/C,MAAM,CAAC0C,UAAP,GAAoBrG,oCAArC,AAAA;gBACA,MAAM2G,YAAY,GAAGnQ,0BAAK,CAACgQ,KAAD,EAAQ;oBAACxG,oCAAD;oBAAiB0G,QAAQ,GAAGR,YAA5B;iBAAR,CAA1B,AAAA;gBAEAd,cAAc,CAACpG,KAAf,CAAqBuH,QAArB,GAAgCP,eAAe,GAAG,IAAlD,CAAAZ;gBACAA,cAAc,CAACpG,KAAf,CAAqBwH,KAArB,GAA6BG,YAAY,GAAG,IAA5C,CAAAvB;aA/BF,CAkCA,4FAFC;YAGD,uBAAA;YACA,4FAAA;YACA,MAAMV,KAAK,GAAGpI,QAAQ,EAAtB,AAAA;YACA,MAAMsK,eAAe,GAAGjD,MAAM,CAACkD,WAAP,GAAqB7G,oCAAc,GAAG,CAA9D,AAAA;YACA,MAAM8G,WAAW,GAAGxF,QAAQ,CAACyB,YAA7B,AAAA;YAEA,MAAMgE,aAAa,GAAGpD,MAAM,CAACqD,gBAAP,CAAwB5F,OAAxB,CAAtB,AAAA;YACA,MAAM6F,qBAAqB,GAAGC,QAAQ,CAACH,aAAa,CAACI,cAAf,EAA+B,EAA/B,CAAtC,AAAA;YACA,MAAMC,iBAAiB,GAAGF,QAAQ,CAACH,aAAa,CAACM,UAAf,EAA2B,EAA3B,CAAlC,AAAA;YACA,MAAMC,wBAAwB,GAAGJ,QAAQ,CAACH,aAAa,CAACQ,iBAAf,EAAkC,EAAlC,CAAzC,AAAA;YACA,MAAMC,oBAAoB,GAAGN,QAAQ,CAACH,aAAa,CAACU,aAAf,EAA8B,EAA9B,CAArC,AAAA;YACA,MAAMC,iBAAiB,GAAGT,qBAAqB,GAAGG,iBAAxB,GAA4CN,WAA5C,GAA0DU,oBAA1D,GAAiFF,wBAA3G,AA9CA,EA8CqI,kBAArI;YACA,MAAMK,gBAAgB,GAAG3J,IAAI,CAAC4J,GAAL,CAASnG,YAAY,CAACoG,YAAb,GAA4B,CAArC,EAAwCH,iBAAxC,CAAzB,AAAA;YAEA,MAAMI,cAAc,GAAGnE,MAAM,CAACqD,gBAAP,CAAwB1F,QAAxB,CAAvB,AAAA;YACA,MAAMyG,kBAAkB,GAAGb,QAAQ,CAACY,cAAc,CAACT,UAAhB,EAA4B,EAA5B,CAAnC,AAAA;YACA,MAAMW,qBAAqB,GAAGd,QAAQ,CAACY,cAAc,CAACL,aAAhB,EAA+B,EAA/B,CAAtC,AAAA;YAEA,MAAMQ,sBAAsB,GAAGzC,WAAW,CAAC0C,GAAZ,GAAkB1C,WAAW,CAAC2C,MAAZ,GAAqB,CAAvC,GAA2CnI,oCAA1E,AAAA;YACA,MAAMoI,yBAAyB,GAAGxB,eAAe,GAAGqB,sBAApD,AAAA;YAEA,MAAMI,sBAAsB,GAAG5G,YAAY,CAACoG,YAAb,GAA4B,CAA3D,AAAA;YACA,MAAMS,gBAAgB,GAAG7G,YAAY,CAAC8G,SAAb,GAAyBF,sBAAlD,AAAA;YACA,MAAMG,sBAAsB,GAAGvB,qBAAqB,GAAGG,iBAAxB,GAA4CkB,gBAA3E,AAAA;YACA,MAAMG,yBAAyB,GAAGf,iBAAiB,GAAGc,sBAAtD,AAAA;YAEA,MAAME,2BAA2B,GAAGF,sBAAsB,IAAIP,sBAA9D,AAAA;YAEA,IAAIS,2BAAJ,EAAiC;gBAC/B,MAAMC,UAAU,GAAGlH,YAAY,KAAKiD,KAAK,CAACA,KAAK,CAAC9F,MAAN,GAAe,CAAhB,CAAL,CAAwByD,GAAxB,CAA4BvE,OAAhE,AAAA;gBACAsH,cAAc,CAACpG,KAAf,CAAqB4J,MAArB,GAA8B,KAA9B,CAAAxD;gBACA,MAAMyD,oBAAoB,GACxBzH,OAAO,CAAC0H,YAAR,GAAuBxH,QAAQ,CAACiH,SAAhC,GAA4CjH,QAAQ,CAACuG,YADvD,AAAA;gBAEA,MAAMkB,gCAAgC,GAAG/K,IAAI,CAACmI,GAAL,CACvCiC,yBADuC,EAEvCC,sBAAsB,GAEnBM,CAAAA,UAAU,GAAGX,qBAAH,GAA2B,CAFlB,CAAA,GAGpBa,oBAHF,GAIEvB,wBANqC,CAAzC,AAIKqB;gBAIL,MAAMR,MAAM,GAAGK,sBAAsB,GAAGO,gCAAxC,AAAA;gBACA3D,cAAc,CAACpG,KAAf,CAAqBmJ,MAArB,GAA8BA,MAAM,GAAG,IAAvC,CAAA/C;aAdF,MAeO;gBACL,MAAM4D,WAAW,GAAGvH,YAAY,KAAKiD,KAAK,CAAC,CAAD,CAAL,CAASrC,GAAT,CAAavE,OAAlD,AAAA;gBACAsH,cAAc,CAACpG,KAAf,CAAqBkJ,GAArB,GAA2B,KAA3B,CAAA9C;gBACA,MAAM6D,6BAA6B,GAAGjL,IAAI,CAACmI,GAAL,CACpC8B,sBADoC,EAEpChB,qBAAqB,GACnB3F,QAAQ,CAACiH,SADX,GAGGS,CAAAA,WAAW,GAAGjB,kBAAH,GAAwB,CAHtC,CAAA,GAIEM,sBANkC,CAAtC,AAKKW;gBAGL,MAAMb,MAAM,GAAGc,6BAA6B,GAAGR,yBAA/C,AAAA;gBACArD,cAAc,CAACpG,KAAf,CAAqBmJ,MAArB,GAA8BA,MAAM,GAAG,IAAvC,CAAA/C;gBACA9D,QAAQ,CAACwB,SAAT,GAAqB0F,sBAAsB,GAAGP,sBAAzB,GAAkD3G,QAAQ,CAACiH,SAAhF,CAAAjH;aACD;YAED8D,cAAc,CAACpG,KAAf,CAAqBkK,MAArB,GAA+B,CAAA,EAAElJ,oCAAe,CAAA,IAAA,CAAhD,CAAAoF;YACAA,cAAc,CAACpG,KAAf,CAAqBmK,SAArB,GAAiCxB,gBAAgB,GAAG,IAApD,CAAAvC;YACAA,cAAc,CAACpG,KAAf,CAAqBoK,SAArB,GAAiCxC,eAAe,GAAG,IAAnD,CAhGA,CAiGA,4FADAxB;YAGAH,QAAQ,KAAA,IAAR,IAAAA,QAAQ,KAAA,KAAA,CAAR,IAAAA,QAAQ,EAAA,CAnGR,CAqGA,qFAFAA;YAGA,6DAAA;YACAoE,qBAAqB,CAAC,IAAO/D,uBAAuB,CAACxH,OAAxB,GAAkC,IAA1C;YAAA,CAArB,CAAAuL;SACD;KAjHc,EAkHd;QACD/M,QADC;QAEDJ,OAAO,CAACvC,OAFP;QAGDuC,OAAO,CAACpC,SAHP;QAIDsL,cAJC;QAKDhE,OALC;QAMDE,QANC;QAODG,YAPC;QAQDE,gBARC;QASDzF,OAAO,CAAC7C,GATP;QAUD4L,QAVC;KAlHc,CAAjB,AAkHC;IAaDxN,kDAAe,CAAC,IAAM4I,QAAQ,EAAf;IAAA,EAAmB;QAACA,QAAD;KAAnB,CAAf,CA3IsE,CA6ItE,uCAFA5I;IAGA,MAAM,CAAC6R,aAAD,EAAgBC,gBAAhB,CAAA,GAAoCjT,qBAAA,EAA1C,AAAA;IACAmB,kDAAe,CAAC,IAAM;QACpB,IAAI2J,OAAJ,EAAamI,gBAAgB,CAAC5F,MAAM,CAACqD,gBAAP,CAAwB5F,OAAxB,CAAA,CAAiCoI,MAAlC,CAAhB,CAAb;KADa,EAEZ;QAACpI,OAAD;KAFY,CAAf,CA/IsE,CAmJtE,oFAFC;IAGD,2FAAA;IACA,oFAAA;IACA,mGAAA;IACA,MAAMqI,wBAAwB,GAAGnT,wBAAA,CAC9BkL,CAAAA,IAAD,GAAgD;QAC9C,IAAIA,IAAI,IAAI+D,mBAAmB,CAACzH,OAApB,KAAgC,IAA5C,EAAkD;YAChDuC,QAAQ,EAARA,CAAAA;YACA2C,iBAAiB,KAAA,IAAjB,IAAAA,iBAAiB,KAAA,KAAA,CAAjB,IAAAA,iBAAiB,EAAjBA,CAAAA;YACAuC,mBAAmB,CAACzH,OAApB,GAA8B,KAA9B,CAAAyH;SACD;KAN4B,EAQ/B;QAAClF,QAAD;QAAW2C,iBAAX;KAR+B,CAAjC,AAOG;IAIH,OAAA,aACE,CAAA,0BAAA,CAAC,4CAAD,EADF;QAEI,KAAK,EAAErK,aADT;QAEE,cAAc,EAAEyM,cAFlB;QAGE,uBAAuB,EAAEE,uBAH3B;QAIE,oBAAoB,EAAEmE,wBAAtB;KAJF,EAAA,aAME,CAAA,0BANF,CAAA,KAAA,EAAA;QAOI,GAAG,EAAEpE,iBADP;QAEE,KAAK,EAAE;YACLd,OAAO,EAAE,MADJ;YAELC,aAAa,EAAE,QAFV;YAGLnE,QAAQ,EAAE,OAHL;YAILmJ,MAAM,EAAEF,aAARE;SAJK;KAFT,EAAA,aASE,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EAAA,2DAAA,CAAA,EAAA,EACMtE,WADN,EATF;QAWI,GAAG,EAAE9I,YAFP;QAGE,KAAK,EAAE;YACL,gFAAA;YACA,2EAAA;YACAsN,SAAS,EAAE,YAHN;YAIL,oEAAA;YACAN,SAAS,EAAE,MALN;YAML,GAAGlE,WAAW,CAAClG,KAAf;SANK;KAHT,CAAA,CATF,CANF,CADF,CAgBM;CArL0B,CAAlC,AAoMC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,+CAAA,EAAA;IAAA,WAAA,EAAA,gDAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAM2K,0CAAoB,GAAG,sBAA7B,AAAA;AAMA,MAAMxF,0CAAoB,GAAA,aAAG7N,CAAAA,uBAAA,CAG3B,CAACoC,KAAD,EAAgDsD,YAAhD,GAAiE;IACjE,MAAM,E,eACJrD,aADI,CAAA,SAEJgI,KAAK,GAAG,OAFJ,qBAGJI,gBAAgB,GAAGf,oCAHf,GAIJ,GAAGkF,WAAH,EAJI,GAKFxM,KALJ,AAAM;IAMN,MAAMgB,WAAW,GAAGrB,oCAAc,CAACM,aAAD,CAAlC,AAAA;IAEA,OAAA,aACE,CAAA,0BAAA,CAAC,iCAAD,EAAA,2DAAA,CAAA,EAAA,EACMe,WADN,EAEMwL,WAFN,EADF;QAII,GAAG,EAAElJ,YAHP;QAIE,KAAK,EAAE2E,KAJT;QAKE,gBAAgB,EAAEI,gBALpB;QAME,KAAK,EAAE;YACL,iDAAA;YACA2I,SAAS,EAAE,YAFN;YAGL,GAAGxE,WAAW,CAAClG,KAHV;YAMH,yCAAA,EAA2C,sCAD1C;YAED,wCAAA,EAA0C,qCAFzC;YAGD,yCAAA,EAA2C,sCAH1C;YAID,8BAAA,EAAgC,kCAJ/B;YAKD,+BAAA,EAAiC,mCAAjC;SAVG;KANT,CAAA,CADF,CACE;CAbyB,CAA7B,AAkCC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,0CAAA,EAAA;IAAA,WAAA,EAAA,0CAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAQA,MAAM,CAAC4K,4CAAD,EAAyBC,8CAAzB,CAAA,GACJzR,yCAAmB,CAA6BsH,kCAA7B,EAA2C,EAA3C,CADrB,AAAA;AAGA,MAAMoK,mCAAa,GAAG,gBAAtB,AAAA;AAMA,MAAMlV,yCAAc,GAAA,aAAG0B,CAAAA,uBAAA,CACrB,CAACoC,KAAD,EAA0CsD,YAA1C,GAA2D;IACzD,MAAM,E,eAAErD,aAAF,CAAA,EAAiB,GAAGoR,aAAH,EAAjB,GAAsCrR,KAA5C,AAAM;IACN,MAAMyM,cAAc,GAAGjF,6CAAuB,CAAC4J,mCAAD,EAAgBnR,aAAhB,CAA9C,AAAA;IACA,MAAMqR,eAAe,GAAGH,8CAAwB,CAACC,mCAAD,EAAgBnR,aAAhB,CAAhD,AAAA;IACA,MAAMyD,YAAY,GAAGzF,8CAAe,CAACqF,YAAD,EAAemJ,cAAc,CAAC8E,gBAA9B,CAApC,AAAA;IACA,MAAMC,gBAAgB,GAAG5T,mBAAA,CAAa,CAAb,CAAzB,AAAA;IACA,OAAA,aACE,CAAA,0BAAA,CAAA,qBAAA,EAAA,IAAA,EAAA,aAEE,CAAA,0BAHJ,CAAA,OAAA,EAAA;QAIM,uBAAuB,EAAE;YACvB6T,MAAM,EAAG,CAATA,yKAAAA,CAAAA;SADuB;KAD3B,CAFF,EAAA,aAOE,CAAA,0BAAA,CAAC,gCAAD,CAAY,IAAZ,EALA;QAKiB,KAAK,EAAExR,aAAP;KAAjB,EAAA,aACE,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EADF,2DAAA,CAAA;QAEI,4BAAA,EAA2B,EAD7B;QAEE,IAAI,EAAC,cAAL;KAFF,EAGMoR,aAHN,EAAA;QAIE,GAAG,EAAE3N,YAJP;QAKE,KAAK,EAAE;YACL,0EAAA;YACA,mFAAA;YACA,uCAAA;YACAiE,QAAQ,EAAE,UAJL;YAKL+J,IAAI,EAAE,CALD;YAMLC,QAAQ,EAAE,MANL;YAOL,GAAGN,aAAa,CAAC/K,KAAjB;SAZJ;QAcE,QAAQ,EAAEvI,4CAAoB,CAACsT,aAAa,CAACO,QAAf,EAA0B3O,CAAAA,KAAD,GAAW;YAChE,MAAM2F,QAAQ,GAAG3F,KAAK,CAAC2B,aAAvB,AAAA;YACA,MAAM,E,gBAAE8H,cAAF,CAAA,E,yBAAkBE,uBAAAA,CAAAA,EAAlB,GAA8C0E,eAApD,AAAM;YACN,IAAI1E,uBAAuB,KAAA,IAAvB,IAAAA,uBAAuB,KAAA,KAAA,CAAvB,IAAAA,uBAAuB,CAAExH,OAAzB,IAAoCsH,cAAxC,EAAwD;gBACtD,MAAMmF,UAAU,GAAGvM,IAAI,CAACmF,GAAL,CAAS+G,gBAAgB,CAACpM,OAAjB,GAA2BwD,QAAQ,CAACwB,SAA7C,CAAnB,AAAA;gBACA,IAAIyH,UAAU,GAAG,CAAjB,EAAoB;oBAClB,MAAM3D,eAAe,GAAGjD,MAAM,CAACkD,WAAP,GAAqB7G,oCAAc,GAAG,CAA9D,AAAA;oBACA,MAAMwK,YAAY,GAAGC,UAAU,CAACrF,cAAc,CAACpG,KAAf,CAAqBmK,SAAtB,CAA/B,AAAA;oBACA,MAAMuB,SAAS,GAAGD,UAAU,CAACrF,cAAc,CAACpG,KAAf,CAAqBmJ,MAAtB,CAA5B,AAAA;oBACA,MAAMwC,UAAU,GAAG3M,IAAI,CAACmI,GAAL,CAASqE,YAAT,EAAuBE,SAAvB,CAAnB,AAAA;oBAEA,IAAIC,UAAU,GAAG/D,eAAjB,EAAkC;wBAChC,MAAMgE,UAAU,GAAGD,UAAU,GAAGJ,UAAhC,AAAA;wBACA,MAAMM,iBAAiB,GAAG7M,IAAI,CAAC4J,GAAL,CAAShB,eAAT,EAA0BgE,UAA1B,CAA1B,AAAA;wBACA,MAAME,UAAU,GAAGF,UAAU,GAAGC,iBAAhC,AAAA;wBAEAzF,cAAc,CAACpG,KAAf,CAAqBmJ,MAArB,GAA8B0C,iBAAiB,GAAG,IAAlD,CAAAzF;wBACA,IAAIA,cAAc,CAACpG,KAAf,CAAqB4J,MAArB,KAAgC,KAApC,EAA2C;4BACzCtH,QAAQ,CAACwB,SAAT,GAAqBgI,UAAU,GAAG,CAAb,GAAiBA,UAAjB,GAA8B,CAAnD,CADyC,CAEzC,gDADAxJ;4BAEA8D,cAAc,CAACpG,KAAf,CAAqB+L,cAArB,GAAsC,UAAtC,CAAA3F;yBACD;qBACF;iBACF;aACF;YACD8E,gBAAgB,CAACpM,OAAjB,GAA2BwD,QAAQ,CAACwB,SAApC,CAAAoH;SAzB4B,CA0B7B;KAxCH,CAAA,CADF,CAPF,CADF,CASM;CAhBa,CAAvB,AA6DG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,mCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMc,gCAAU,GAAG,aAAnB,AAAA;AAIA,MAAM,CAACC,gDAAD,EAA6BC,2CAA7B,CAAA,GACJ9S,yCAAmB,CAA0B4S,gCAA1B,CADrB,AAAA;AAMA,MAAMnW,yCAAW,GAAA,aAAGyB,CAAAA,uBAAA,CAClB,CAACoC,KAAD,EAAuCsD,YAAvC,GAAwD;IACtD,MAAM,E,eAAErD,aAAF,CAAA,EAAiB,GAAGwS,UAAH,EAAjB,GAAmCzS,KAAzC,AAAM;IACN,MAAM0S,OAAO,GAAGnU,2BAAK,EAArB,AAAA;IACA,OAAA,aACE,CAAA,0BAAA,CAAC,gDAAD,EADF;QAC8B,KAAK,EAAE0B,aAAnC;QAAkD,EAAE,EAAEyS,OAAJ;KAAlD,EAAA,aACE,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EADF,2DAAA,CAAA;QACiB,IAAI,EAAC,OAApB;QAA4B,iBAAA,EAAiBA,OAAjB;KAA5B,EAA0DD,UAA1D,EAAA;QAAsE,GAAG,EAAEnP,YAAL;KAAtE,CAAA,CADF,CADF,CAEI;CANY,CAApB,AASG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,gCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMqP,gCAAU,GAAG,aAAnB,AAAA;AAKA,MAAMvW,yCAAW,GAAA,aAAGwB,CAAAA,uBAAA,CAClB,CAACoC,KAAD,EAAuCsD,YAAvC,GAAwD;IACtD,MAAM,E,eAAErD,aAAF,CAAA,EAAiB,GAAG2S,UAAH,EAAjB,GAAmC5S,KAAzC,AAAM;IACN,MAAM6S,YAAY,GAAGL,2CAAqB,CAACG,gCAAD,EAAa1S,aAAb,CAA1C,AAAA;IACA,OAAA,aAAO,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EAAP,2DAAA,CAAA;QAAsB,EAAE,EAAE4S,YAAY,CAACC,EAAjB;KAAf,EAAwCF,UAAxC,EAAA;QAAoD,GAAG,EAAEtP,YAAL;KAApD,CAAA,CAAP,CAAO;CAJS,CAApB,AAKG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,gCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMyP,+BAAS,GAAG,YAAlB,AAAA;AAUA,MAAM,CAACC,+CAAD,EAA4BC,0CAA5B,CAAA,GACJvT,yCAAmB,CAAyBqT,+BAAzB,CADrB,AAAA;AAUA,MAAM1W,yCAAU,GAAA,aAAGuB,CAAAA,uBAAA,CACjB,CAACoC,KAAD,EAAsCsD,YAAtC,GAAuD;IACrD,MAAM,E,eACJrD,aADI,CAAA,E,OAEJM,KAFI,CAAA,YAGJO,QAAQ,GAAG,KAHP,GAIJoS,SAAS,EAAEC,aAJP,CAAA,EAKJ,GAAGC,SAAH,EALI,GAMFpT,KANJ,AAAM;IAON,MAAMwD,OAAO,GAAG3D,sCAAgB,CAACkT,+BAAD,EAAY9S,aAAZ,CAAhC,AAAA;IACA,MAAMwM,cAAc,GAAGjF,6CAAuB,CAACuL,+BAAD,EAAY9S,aAAZ,CAA9C,AAAA;IACA,MAAMoT,UAAU,GAAG7P,OAAO,CAACjD,KAAR,KAAkBA,KAArC,AAAA;IACA,MAAM,CAAC2S,SAAD,EAAYI,YAAZ,CAAA,GAA4B1V,qBAAA,CAAeuV,aAAf,KAAA,IAAA,IAAeA,aAAf,KAAA,KAAA,CAAA,GAAeA,aAAf,GAAgC,EAAhC,CAAlC,AAAA;IACA,MAAM,CAACI,SAAD,EAAYC,YAAZ,CAAA,GAA4B5V,qBAAA,CAAe,KAAf,CAAlC,AAAA;IACA,MAAM8F,YAAY,GAAGzF,8CAAe,CAACqF,YAAD,EAAgBwF,CAAAA,IAAD,GAAnD;QAAmD,IAAA,qBAAA,AAAA;QAAA,OAAA,AAAA,CAAA,qBAAA,GACjD2D,cAAc,CAACtB,eADkC,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GACjD,qBAAA,CAAA,IAAA,CAAAsB,cAAc,EAAmB3D,IAAnB,EAAyBvI,KAAzB,EAAgCO,QAAhC,CADmC,CAAA;KAAf,CAApC,AAAmD;IAGnD,MAAM2S,MAAM,GAAGlV,2BAAK,EAApB,AAAA;IAEA,MAAMmV,YAAY,GAAG,IAAM;QACzB,IAAI,CAAC5S,QAAL,EAAe;YACb0C,OAAO,CAAC9C,aAAR,CAAsBH,KAAtB,CAAAiD,CAAAA;YACAA,OAAO,CAAClD,YAAR,CAAqB,KAArB,CAAAkD,CAAAA;SACD;KAJH,AAKC;IAED,OAAA,aACE,CAAA,0BAAA,CAAC,+CAAD,EADF;QAEI,KAAK,EAAEvD,aADT;QAEE,KAAK,EAAEM,KAFT;QAGE,QAAQ,EAAEO,QAHZ;QAIE,MAAM,EAAE2S,MAJV;QAKE,UAAU,EAAEJ,UALd;QAME,gBAAgB,EAAEzV,wBAAA,CAAmBkL,CAAAA,IAAD,GAAU;YAC5CwK,YAAY,CAAEK,CAAAA,aAAD,GAAbL;gBAAa,IAAA,iBAAA,AAAA;gBAAA,OAAmBK,aAAa,IAAI,AAAA,CAAA,AAAA,CAAA,iBAAA,GAAC7K,IAAD,KAAA,IAAA,IAACA,IAAD,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAACA,IAAI,CAAE8K,WAAP,CAAA,KAAA,IAAA,IAAA,iBAAA,KAAA,KAAA,CAAA,GAAA,iBAAA,GAAsB,EAAtB,CAAA,CAA0BC,IAA1B,EAApC,CAAA;aAAD,CAAZ,CAAa;SADG,EAEf,EAFe,CAEjB;KARH,EAAA,aAUE,CAAA,0BAAA,CAAC,gCAAD,CAAY,QAAZ,EAVF;QAWI,KAAK,EAAE5T,aADT;QAEE,KAAK,EAAEM,KAFT;QAGE,QAAQ,EAAEO,QAHZ;QAIE,SAAS,EAAEoS,SAAX;KAJF,EAAA,aAME,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EANF,2DAAA,CAAA;QAOI,IAAI,EAAC,QADP;QAEE,iBAAA,EAAiBO,MAFnB;QAGE,kBAAA,EAAkBF,SAAS,GAAG,EAAH,GAAQpQ,SAHrC,CAIE,mDADA;QAHF;QAKE,eAAA,EAAekQ,UAAU,IAAIE,SAL/B;QAME,YAAA,EAAYF,UAAU,GAAG,SAAH,GAAe,WANvC;QAOE,eAAA,EAAevS,QAAQ,IAAIqC,SAP7B;QAQE,eAAA,EAAerC,QAAQ,GAAG,EAAH,GAAQqC,SARjC;QASE,QAAQ,EAAErC,QAAQ,GAAGqC,SAAH,GAAe,EAAjC;KATF,EAUMiQ,SAVN,EAAA;QAWE,GAAG,EAAE1P,YAXP;QAYE,OAAO,EAAE3F,4CAAoB,CAACqV,SAAS,CAACU,OAAX,EAAoB,IAAMN,YAAY,CAAC,IAAD,CAAtC;QAAA,CAZ/B;QAaE,MAAM,EAAEzV,4CAAoB,CAACqV,SAAS,CAACW,MAAX,EAAmB,IAAMP,YAAY,CAAC,KAAD,CAArC;QAAA,CAb9B;QAcE,WAAW,EAAEzV,4CAAoB,CAACqV,SAAS,CAACY,WAAX,EAAwBN,YAAxB,CAdnC;QAeE,aAAa,EAAE3V,4CAAoB,CAACqV,SAAS,CAACa,aAAX,EAA2BhR,CAAAA,KAAD,GAAW;YACtE,IAAInC,QAAJ,EAAc;gBAAA,IAAA,qBAAA,AAAA;gBACZ,CAAA,qBAAA,GAAA2L,cAAc,CAACyH,WAAf,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,IAAA,qBAAA,CAAA,IAAA,CAAAzH,cAAc,CAAd,CAAA;aADF,MAGE,kEAAA;YACA,wEAAA;YACAxJ,KAAK,CAAC2B,aAAN,CAAoBC,KAApB,CAA0B;gBAAE+G,aAAa,EAAE,IAAfA;aAA5B,CAA0B,CAAA;SANK,CAfrC;QAwBE,cAAc,EAAE7N,4CAAoB,CAACqV,SAAS,CAACe,cAAX,EAA4BlR,CAAAA,KAAD,GAAW;YACxE,IAAIA,KAAK,CAAC2B,aAAN,KAAwBmF,QAAQ,CAACC,aAArC,EAAoD;gBAAA,IAAA,sBAAA,AAAA;gBAClD,CAAA,sBAAA,GAAAyC,cAAc,CAACyH,WAAf,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,IAAA,sBAAA,CAAA,IAAA,CAAAzH,cAAc,CAAd,CAAA;aACD;SAHiC,CAxBtC;QA6BE,SAAS,EAAE1O,4CAAoB,CAACqV,SAAS,CAACxN,SAAX,EAAuB3C,CAAAA,KAAD,GAAW;YAAA,IAAA,qBAAA,AAAA;YAC9D,MAAM4C,aAAa,GAAG,AAAA,CAAA,AAAA,CAAA,qBAAA,GAAA4G,cAAc,CAAC5I,SAAf,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAA0BuB,OAA1B,CAAA,KAAsC,EAA5D,AAAA;YACA,IAAIS,aAAa,IAAI5C,KAAK,CAACgD,GAAN,KAAc,GAAnC,EAAwC,OAAxC;YACA,IAAI5G,oCAAc,CAAC8G,QAAf,CAAwBlD,KAAK,CAACgD,GAA9B,CAAJ,EAAwCyN,YAAY,EAAA,CAHU,CAI9D,+DADA;YAEA,IAAIzQ,KAAK,CAACgD,GAAN,KAAc,GAAlB,EAAuBhD,KAAK,CAAC0C,cAAN,EAAvB,CAAA;SAL6B,CAM9B;KAnCH,CAAA,CANF,CAVF,CADF,CAiBM;CA3CS,CAAnB,AAmFG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,+BAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMyO,oCAAc,GAAG,gBAAvB,AAAA;AAKA,MAAM9X,yCAAc,GAAA,aAAGsB,CAAAA,uBAAA,CACrB,CAACoC,KAAD,EAA0CsD,YAA1C,GAA2D;IACzD,sEAAA;IACA,MAAM,E,eAAErD,aAAF,CAAA,E,WAAiBoG,SAAjB,CAAA,E,OAA4BC,KAA5B,CAAA,EAAmC,GAAG+N,aAAH,EAAnC,GAAwDrU,KAA9D,AAAM;IACN,MAAMwD,OAAO,GAAG3D,sCAAgB,CAACuU,oCAAD,EAAiBnU,aAAjB,CAAhC,AAAA;IACA,MAAMwM,cAAc,GAAGjF,6CAAuB,CAAC4M,oCAAD,EAAiBnU,aAAjB,CAA9C,AAAA;IACA,MAAMqU,WAAW,GAAGrB,0CAAoB,CAACmB,oCAAD,EAAiBnU,aAAjB,CAAxC,AAAA;IACA,MAAMsU,oBAAoB,GAAGxU,mDAA6B,CAACqU,oCAAD,EAAiBnU,aAAjB,CAA1D,AAAA;IACA,MAAM,CAACuU,YAAD,EAAeC,eAAf,CAAA,GAAkC7W,qBAAA,CAA6C,IAA7C,CAAxC,AAAA;IACA,MAAM8F,YAAY,GAAGzF,8CAAe,CAClCqF,YADkC,EAEjCwF,CAAAA,IAAD,GAAU2L,eAAe,CAAC3L,IAAD,CAFS;IAAA,EAGlCwL,WAAW,CAACI,gBAHsB,EAIjC5L,CAAAA,IAAD,GAJF;QAIE,IAAA,qBAAA,AAAA;QAAA,OAAA,AAAA,CAAA,qBAAA,GAAU2D,cAAc,CAAClB,mBAAzB,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAU,qBAAA,CAAA,IAAA,CAAAkB,cAAc,EAAuB3D,IAAvB,EAA6BwL,WAAW,CAAC/T,KAAzC,EAAgD+T,WAAW,CAACxT,QAA5D,CAAxB,CAAA;KAJkC,CAApC,AAIE;IAGF,MAAM8S,WAAW,GAAGY,YAAH,KAAA,IAAA,IAAGA,YAAH,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAGA,YAAY,CAAEZ,WAAlC,AAAA;IACA,MAAMe,YAAY,GAAG/W,oBAAA,CACnB,IAAA,aACE,CAAA,0BAFJ,CAAA,QAAA,EAAA;YAEY,GAAG,EAAE0W,WAAW,CAAC/T,KAAzB;YAAgC,KAAK,EAAE+T,WAAW,CAAC/T,KAAnD;YAA0D,QAAQ,EAAE+T,WAAW,CAACxT,QAAtB;SAA1D,EACG8S,WADH,CAFiB;IAAA,EAMnB;QAACU,WAAW,CAACxT,QAAb;QAAuBwT,WAAW,CAAC/T,KAAnC;QAA0CqT,WAA1C;KANmB,CAArB,AAEI;IAOJ,MAAM,E,mBAAEiB,iBAAF,CAAA,E,sBAAqBC,oBAAAA,CAAAA,EAArB,GAA8CP,oBAApD,AAAM;IACNxV,kDAAe,CAAC,IAAM;QACpB8V,iBAAiB,CAACF,YAAD,CAAjB,CAAAE;QACA,OAAO,IAAMC,oBAAoB,CAACH,YAAD,CAAjC;QAAA,CAAA;KAFa,EAGZ;QAACE,iBAAD;QAAoBC,oBAApB;QAA0CH,YAA1C;KAHY,CAAf,CAGC;IAED,OAAA,aACE,CAAA,0BAAA,CAAA,qBAAA,EAAA,IAAA,EAAA,aACE,CAAA,0BAAA,CAAC,sCAAD,CAAW,IAAX,EAFJ,2DAAA,CAAA;QAEoB,EAAE,EAAEL,WAAW,CAACb,MAAhB;KAAhB,EAA4CY,aAA5C,EAAA;QAA2D,GAAG,EAAE3Q,YAAL;KAA3D,CAAA,CADF,EAIG4Q,WAAW,CAACjB,UAAZ,IAA0B7P,OAAO,CAACpC,SAAlC,IAA+C,CAACoC,OAAO,CAAClC,oBAAxD,GAAA,aACGzD,CAAAA,4BAAA,CAAsBwW,aAAa,CAACnU,QAApC,EAA8CsD,OAAO,CAACpC,SAAtD,CADH,GAEG,IANN,CADF,CAEI;CAlCe,CAAvB,AA0CG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,oCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAM2T,yCAAmB,GAAG,qBAA5B,AAAA;AAKA,MAAMxY,yCAAmB,GAAA,aAAGqB,CAAAA,uBAAA,CAC1B,CAACoC,KAAD,EAA+CsD,YAA/C,GAAgE;IAC9D,MAAM,E,eAAErD,aAAF,CAAA,EAAiB,GAAG+U,kBAAH,EAAjB,GAA2ChV,KAAjD,AAAM;IACN,MAAMsU,WAAW,GAAGrB,0CAAoB,CAAC8B,yCAAD,EAAsB9U,aAAtB,CAAxC,AAAA;IACA,OAAOqU,WAAW,CAACjB,UAAZ,GAAA,aACL,CAAA,0BAAA,CAAC,sCAAD,CAAW,IAAX,EADF,2DAAA,CAAA;QACkB,aAAA,EAAA,IAAA;KAAhB,EAAgC2B,kBAAhC,EAAA;QAAoD,GAAG,EAAE1R,YAAL;KAApD,CAAA,CADK,GAEH,IAFJ,CACE;CALsB,CAA5B,AAOG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,yCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAM2R,2CAAqB,GAAG,sBAA9B,AAAA;AAKA,MAAMzY,yCAAoB,GAAA,aAAGoB,CAAAA,uBAAA,CAG3B,CAACoC,KAAD,EAAgDsD,YAAhD,GAAiE;IACjE,MAAMmJ,cAAc,GAAGjF,6CAAuB,CAACyN,2CAAD,EAAwBjV,KAAK,CAACC,aAA9B,CAA9C,AAAA;IACA,MAAMqR,eAAe,GAAGH,8CAAwB,CAAC8D,2CAAD,EAAwBjV,KAAK,CAACC,aAA9B,CAAhD,AAAA;IACA,MAAM,CAACiV,YAAD,EAAcC,cAAd,CAAA,GAAgCvX,qBAAA,CAAe,KAAf,CAAtC,AAAA;IACA,MAAM8F,YAAY,GAAGzF,8CAAe,CAACqF,YAAD,EAAegO,eAAe,CAAC8D,oBAA/B,CAApC,AAAA;IAEArW,kDAAe,CAAC,IAAM;QACpB,IAAI0N,cAAc,CAAC7D,QAAf,IAA2B6D,cAAc,CAACtD,YAA9C,EAA4D;YAC1D,MAAMP,QAAQ,GAAG6D,cAAc,CAAC7D,QAAhC,AAAA;YACA,SAASyM,YAAT,GAAwB;gBACtB,MAAMH,WAAW,GAAGtM,QAAQ,CAACwB,SAAT,GAAqB,CAAzC,AAAA;gBACA+K,cAAc,CAACD,WAAD,CAAd,CAAAC;aACD;YACDE,YAAY,EAAZA,CAAAA;YACAzM,QAAQ,CAACiC,gBAAT,CAA0B,QAA1B,EAAoCwK,YAApC,CAAAzM,CAAAA;YACA,OAAO,IAAMA,QAAQ,CAACgC,mBAAT,CAA6B,QAA7B,EAAuCyK,YAAvC,CAAb;YAAA,CAAA;SACD;KAVY,EAWZ;QAAC5I,cAAc,CAAC7D,QAAhB;QAA0B6D,cAAc,CAACtD,YAAzC;KAXY,CAAf,CAWC;IAED,OAAO+L,YAAW,GAAA,aAChB,CAAA,0BAAA,CAAC,4CAAD,EAAA,2DAAA,CAAA,EAAA,EACMlV,KADN,EADF;QAGI,GAAG,EAAE0D,YAFP;QAGE,YAAY,EAAE,IAAM;YAClB,MAAM,E,UAAEkF,QAAF,CAAA,E,cAAYG,YAAAA,CAAAA,EAAZ,GAA6B0D,cAAnC,AAAM;YACN,IAAI7D,QAAQ,IAAIG,YAAhB,EACEH,QAAQ,CAACwB,SAAT,GAAqBxB,QAAQ,CAACwB,SAAT,GAAqBrB,YAAY,CAACoG,YAAvD,CAAAvG;SAEH;KARH,CAAA,CADgB,GAWd,IAXJ,CACE;CAvByB,CAA7B,AAkCC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,2CAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAM0M,6CAAuB,GAAG,wBAAhC,AAAA;AAKA,MAAM7Y,wCAAsB,GAAA,aAAGmB,CAAAA,uBAAA,CAG7B,CAACoC,KAAD,EAAkDsD,YAAlD,GAAmE;IACnE,MAAMmJ,cAAc,GAAGjF,6CAAuB,CAAC8N,6CAAD,EAA0BtV,KAAK,CAACC,aAAhC,CAA9C,AAAA;IACA,MAAMqR,eAAe,GAAGH,8CAAwB,CAACmE,6CAAD,EAA0BtV,KAAK,CAACC,aAAhC,CAAhD,AAAA;IACA,MAAM,CAACsV,cAAD,EAAgBC,gBAAhB,CAAA,GAAoC5X,qBAAA,CAAe,KAAf,CAA1C,AAAA;IACA,MAAM8F,YAAY,GAAGzF,8CAAe,CAACqF,YAAD,EAAegO,eAAe,CAAC8D,oBAA/B,CAApC,AAAA;IAEArW,kDAAe,CAAC,IAAM;QACpB,IAAI0N,cAAc,CAAC7D,QAAf,IAA2B6D,cAAc,CAACtD,YAA9C,EAA4D;YAC1D,MAAMP,QAAQ,GAAG6D,cAAc,CAAC7D,QAAhC,AAAA;YACA,SAASyM,YAAT,GAAwB;gBACtB,MAAMI,SAAS,GAAG7M,QAAQ,CAACyB,YAAT,GAAwBzB,QAAQ,CAACwH,YAAnD,AADsB,EAEtB,uDADA;gBAEA,mDAAA;gBACA,MAAMmF,aAAa,GAAGjQ,IAAI,CAACoQ,IAAL,CAAU9M,QAAQ,CAACwB,SAAnB,CAAA,GAAgCqL,SAAtD,AAAA;gBACAD,gBAAgB,CAACD,aAAD,CAAhB,CAAAC;aACD;YACDH,YAAY,EAAZA,CAAAA;YACAzM,QAAQ,CAACiC,gBAAT,CAA0B,QAA1B,EAAoCwK,YAApC,CAAAzM,CAAAA;YACA,OAAO,IAAMA,QAAQ,CAACgC,mBAAT,CAA6B,QAA7B,EAAuCyK,YAAvC,CAAb;YAAA,CAAA;SACD;KAbY,EAcZ;QAAC5I,cAAc,CAAC7D,QAAhB;QAA0B6D,cAAc,CAACtD,YAAzC;KAdY,CAAf,CAcC;IAED,OAAOoM,cAAa,GAAA,aAClB,CAAA,0BAAA,CAAC,4CAAD,EAAA,2DAAA,CAAA,EAAA,EACMvV,KADN,EADF;QAGI,GAAG,EAAE0D,YAFP;QAGE,YAAY,EAAE,IAAM;YAClB,MAAM,E,UAAEkF,QAAF,CAAA,E,cAAYG,YAAAA,CAAAA,EAAZ,GAA6B0D,cAAnC,AAAM;YACN,IAAI7D,QAAQ,IAAIG,YAAhB,EACEH,QAAQ,CAACwB,SAAT,GAAqBxB,QAAQ,CAACwB,SAAT,GAAqBrB,YAAY,CAACoG,YAAvD,CAAAvG;SAEH;KARH,CAAA,CADkB,GAWhB,IAXJ,CACE;CA1B2B,CAA/B,AAqCC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,wCAAA,EAAA;IAAA,WAAA,EAAA,6CAAA;CAAA,CAAA,CAAA;AAOA,MAAM+M,4CAAsB,GAAA,aAAG/X,CAAAA,uBAAA,CAG7B,CAACoC,KAAD,EAAkDsD,YAAlD,GAAmE;IACnE,MAAM,E,eAAErD,aAAF,CAAA,E,cAAiB2V,YAAjB,CAAA,EAA+B,GAAGC,oBAAH,EAA/B,GAA2D7V,KAAjE,AAAM;IACN,MAAMyM,cAAc,GAAGjF,6CAAuB,CAAC,oBAAD,EAAuBvH,aAAvB,CAA9C,AAAA;IACA,MAAM6V,kBAAkB,GAAGlY,mBAAA,CAA4B,IAA5B,CAA3B,AAAA;IACA,MAAMgG,QAAQ,GAAGpE,mCAAa,CAACS,aAAD,CAA9B,AAAA;IAEA,MAAM8V,oBAAoB,GAAGnY,wBAAA,CAAkB,IAAM;QACnD,IAAIkY,kBAAkB,CAAC1Q,OAAnB,KAA+B,IAAnC,EAAyC;YACvC6F,MAAM,CAAC+K,aAAP,CAAqBF,kBAAkB,CAAC1Q,OAAxC,CAAA6F,CAAAA;YACA6K,kBAAkB,CAAC1Q,OAAnB,GAA6B,IAA7B,CAAA0Q;SACD;KAJ0B,EAK1B,EAL0B,CAA7B,AAKC;IAEDlY,sBAAA,CAAgB,IAAM;QACpB,OAAO,IAAMmY,oBAAoB,EAAjC;QAAA,CAAA;KADF,EAEG;QAACA,oBAAD;KAFH,CAAA,CAbmE,CAiBnE,8FAFC;IAGD,kGAAA;IACA,qFAAA;IACA,kFAAA;IACAhX,kDAAe,CAAC,IAAM;QAAA,IAAA,qBAAA,AAAA;QACpB,MAAMkX,UAAU,GAAGrS,QAAQ,EAAA,CAAGU,IAAX,CAAiBF,CAAAA,IAAD,GAAUA,IAAI,CAACuF,GAAL,CAASvE,OAAT,KAAqB2E,QAAQ,CAACC,aAAxD;QAAA,CAAnB,AAAA;QACAiM,UAAU,KAAA,IAAV,IAAAA,UAAU,KAAA,KAAA,CAAV,IAAA,AAAA,CAAA,qBAAA,GAAAA,UAAU,CAAEtM,GAAZ,CAAgBvE,OAAhB,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,IAAA,qBAAA,CAAyB8E,cAAzB,CAAwC;YAAEC,KAAK,EAAE,SAAPA;SAA1C,CAAwC,CAAA;KAF3B,EAGZ;QAACvG,QAAD;KAHY,CAAf,CAGC;IAED,OAAA,aACE,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EADF,2DAAA,CAAA;QAEI,aAAA,EAAA,IAAA;KADF,EAEMiS,oBAFN,EAAA;QAGE,GAAG,EAAEvS,YAHP;QAIE,KAAK,EAAE;YAAE4S,UAAU,EAAE,CAAd;YAAiB,GAAGL,oBAAoB,CAACvP,KAAxB;SAJ1B;QAKE,aAAa,EAAEvI,4CAAoB,CAAC8X,oBAAoB,CAAC/Q,aAAtB,EAAqC,IAAM;YAC5E,IAAIgR,kBAAkB,CAAC1Q,OAAnB,KAA+B,IAAnC,EACE0Q,kBAAkB,CAAC1Q,OAAnB,GAA6B6F,MAAM,CAACkL,WAAP,CAAmBP,YAAnB,EAAiC,EAAjC,CAA7B,CAAAE;SAF+B,CALrC;QAUE,aAAa,EAAE/X,4CAAoB,CAAC8X,oBAAoB,CAAC5B,aAAtB,EAAqC,IAAM;YAAA,IAAA,sBAAA,AAAA;YAC5E,CAAA,sBAAA,GAAAxH,cAAc,CAACyH,WAAf,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,IAAA,sBAAA,CAAA,IAAA,CAAAzH,cAAc,CAAd,CAAA;YACA,IAAIqJ,kBAAkB,CAAC1Q,OAAnB,KAA+B,IAAnC,EACE0Q,kBAAkB,CAAC1Q,OAAnB,GAA6B6F,MAAM,CAACkL,WAAP,CAAmBP,YAAnB,EAAiC,EAAjC,CAA7B,CAAAE;SAH+B,CAVrC;QAgBE,cAAc,EAAE/X,4CAAoB,CAAC8X,oBAAoB,CAAC1B,cAAtB,EAAsC,IAAM;YAC9E4B,oBAAoB,EAApBA,CAAAA;SADkC,CAEnC;KAlBH,CAAA,CADF,CACE;CA9B2B,CAA/B,AAmDC;AAED;;oGAEA,CAEA,MAAMK,oCAAc,GAAG,iBAAvB,AAAA;AAKA,MAAM1Z,wCAAe,GAAA,aAAGkB,CAAAA,uBAAA,CACtB,CAACoC,KAAD,EAA2CsD,YAA3C,GAA4D;IAC1D,MAAM,E,eAAErD,aAAF,CAAA,EAAiB,GAAGoW,cAAH,EAAjB,GAAuCrW,KAA7C,AAAM;IACN,OAAA,aAAO,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EAAP,2DAAA,CAAA;QAAsB,aAAA,EAAA,IAAA;KAAf,EAA+BqW,cAA/B,EAAA;QAA+C,GAAG,EAAE/S,YAAL;KAA/C,CAAA,CAAP,CAAO;CAHa,CAAxB,AAIG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,wCAAA,EAAA;IAAA,WAAA,EAAA,oCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMgT,gCAAU,GAAG,aAAnB,AAAA;AAMA,MAAM3Z,yCAAW,GAAA,aAAGiB,CAAAA,uBAAA,CAClB,CAACoC,KAAD,EAAuCsD,YAAvC,GAAwD;IACtD,MAAM,E,eAAErD,aAAF,CAAA,EAAiB,GAAGsW,UAAH,EAAjB,GAAmCvW,KAAzC,AAAM;IACN,MAAMgB,WAAW,GAAGrB,oCAAc,CAACM,aAAD,CAAlC,AAAA;IACA,MAAMuD,OAAO,GAAG3D,sCAAgB,CAACyW,gCAAD,EAAarW,aAAb,CAAhC,AAAA;IACA,MAAMwM,cAAc,GAAGjF,6CAAuB,CAAC8O,gCAAD,EAAarW,aAAb,CAA9C,AAAA;IACA,OAAOuD,OAAO,CAACrD,IAAR,IAAgBsM,cAAc,CAAC9E,QAAf,KAA4B,QAA5C,GAAA,aACL,CAAA,0BAAA,CAAC,+BAAD,EAAA,2DAAA,CAAA,EAAA,EAA2B3G,WAA3B,EAA4CuV,UAA5C,EADF;QAC0D,GAAG,EAAEjT,YAAL;KAAxD,CAAA,CADK,GAEH,IAFJ,CACE;CAPc,CAApB,AASG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,gCAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAEA,MAAMkT,kCAAY,GAAA,aAAG5Y,CAAAA,uBAAA,CACnB,CAACoC,KAAD,EAAQsD,YAAR,GAAyB;IACvB,MAAM,E,OAAE/C,KAAF,CAAA,EAAS,GAAGkW,WAAH,EAAT,GAA4BzW,KAAlC,AAAM;IACN,MAAM2J,GAAG,GAAG/L,mBAAA,CAAgC,IAAhC,CAAZ,AAAA;IACA,MAAM8F,YAAY,GAAGzF,8CAAe,CAACqF,YAAD,EAAeqG,GAAf,CAApC,AAAA;IACA,MAAM+M,SAAS,GAAG1X,0CAAW,CAACuB,KAAD,CAA7B,AAJuB,EAMvB,yDAFA;IAGA3C,sBAAA,CAAgB,IAAM;QACpB,MAAM+Y,MAAM,GAAGhN,GAAG,CAACvE,OAAnB,AAAA;QACA,MAAMwR,WAAW,GAAG3L,MAAM,CAAC4L,iBAAP,CAAyBC,SAA7C,AAAA;QACA,MAAMC,UAAU,GAAGC,MAAM,CAACC,wBAAP,CACjBL,WADiB,EAEjB,OAFiB,CAAnB,AAAA;QAIA,MAAM/U,QAAQ,GAAGkV,UAAU,CAACG,GAA5B,AAAA;QACA,IAAIR,SAAS,KAAKnW,KAAd,IAAuBsB,QAA3B,EAAqC;YACnC,MAAMoB,KAAK,GAAG,IAAIkU,KAAJ,CAAU,QAAV,EAAoB;gBAAEC,OAAO,EAAE,IAATA;aAAtB,CAAd,AAAkC;YAClCvV,QAAQ,CAACwV,IAAT,CAAcV,MAAd,EAAsBpW,KAAtB,CAAAsB,CAAAA;YACA8U,MAAM,CAACW,aAAP,CAAqBrU,KAArB,CAAA0T,CAAAA;SACD;KAZH,EAaG;QAACD,SAAD;QAAYnW,KAAZ;KAbH,CAaC,CAAA;IAED;;;;;;;;;;;KAWJ,CACI,OAAA,aACE,CAAA,0BAAA,CAAC,gDAAD,EADF;QACkB,OAAO,EAAP,IAAA;KAAhB,EAAA,aACE,CAAA,0BAAA,CAAA,QAAA,EAAA,2DAAA,CAAA,EAAA,EAAYkW,WAAZ,EADF;QAC2B,GAAG,EAAE/S,YAA9B;QAA4C,YAAY,EAAEnD,KAAd;KAA5C,CAAA,CADF,CADF,CAEI;CArCa,CAArB,AAwCG;AAGHiW,kCAAY,CAACe,WAAb,GAA2B,cAA3B,CAAAf;AAEA,SAASxS,wCAAT,CAA4BwT,cAA5B,EAAsE;IACpE,MAAMC,kBAAkB,GAAG5Y,gDAAc,CAAC2Y,cAAD,CAAzC,AAAA;IACA,MAAM3T,SAAS,GAAGjG,mBAAA,CAAa,EAAb,CAAlB,AAAA;IACA,MAAM8Z,QAAQ,GAAG9Z,mBAAA,CAAa,CAAb,CAAjB,AAAA;IAEA,MAAMkG,qBAAqB,GAAGlG,wBAAA,CAC3BqI,CAAAA,GAAD,GAAiB;QACf,MAAMhC,MAAM,GAAGJ,SAAS,CAACuB,OAAV,GAAoBa,GAAnC,AAAA;QACAwR,kBAAkB,CAACxT,MAAD,CAAlB,CAAAwT;QAEC,CAAA,SAASE,YAAT,CAAsBpX,KAAtB,EAAqC;YACpCsD,SAAS,CAACuB,OAAV,GAAoB7E,KAApB,CAAAsD;YACAoH,MAAM,CAAC2M,YAAP,CAAoBF,QAAQ,CAACtS,OAA7B,CAAA,CAFoC,CAGpC,uDADA6F;YAEA,IAAI1K,KAAK,KAAK,EAAd,EAAkBmX,QAAQ,CAACtS,OAAT,GAAmB6F,MAAM,CAACC,UAAP,CAAkB,IAAMyM,YAAY,CAAC,EAAD,CAApC;YAAA,EAA0C,IAA1C,CAAnB,CAAlB;SAJF,CAAA,CAKG1T,MALH,CAKC,CAAA;KAVyB,EAY5B;QAACwT,kBAAD;KAZ4B,CAA9B,AAWG;IAIH,MAAM1T,cAAc,GAAGnG,wBAAA,CAAkB,IAAM;QAC7CiG,SAAS,CAACuB,OAAV,GAAoB,EAApB,CAAAvB;QACAoH,MAAM,CAAC2M,YAAP,CAAoBF,QAAQ,CAACtS,OAA7B,CAAA6F,CAAAA;KAFqB,EAGpB,EAHoB,CAAvB,AAGC;IAEDrN,sBAAA,CAAgB,IAAM;QACpB,OAAO,IAAMqN,MAAM,CAAC2M,YAAP,CAAoBF,QAAQ,CAACtS,OAA7B,CAAb;QAAA,CAAA;KADF,EAEG,EAFH,CAEC,CAAA;IAED,OAAO;QAACvB,SAAD;QAAYC,qBAAZ;QAAmCC,cAAnC;KAAP,CAAA;CACD;AAED;;;;;;;;;;;;;;;;GAgBA,CACA,SAASS,kCAAT,CACEwH,KADF,EAEE/H,MAFF,EAGEI,WAHF,EAIE;IACA,MAAMwT,UAAU,GAAG5T,MAAM,CAACiC,MAAP,GAAgB,CAAhB,IAAqB3D,KAAK,CAACC,IAAN,CAAWyB,MAAX,CAAA,CAAmB6T,KAAnB,CAA0BC,CAAAA,IAAD,GAAUA,IAAI,KAAK9T,MAAM,CAAC,CAAD,CAAlD;IAAA,CAAxC,AAAA;IACA,MAAM+T,gBAAgB,GAAGH,UAAU,GAAG5T,MAAM,CAAC,CAAD,CAAT,GAAeA,MAAlD,AAAA;IACA,MAAMgU,gBAAgB,GAAG5T,WAAW,GAAG2H,KAAK,CAACK,OAAN,CAAchI,WAAd,CAAH,GAAgC,EAApE,AAAA;IACA,IAAI6T,YAAY,GAAGC,+BAAS,CAACnM,KAAD,EAAQ1G,IAAI,CAACmI,GAAL,CAASwK,gBAAT,EAA2B,CAA3B,CAAR,CAA5B,AAAA;IACA,MAAMG,kBAAkB,GAAGJ,gBAAgB,CAAC9R,MAAjB,KAA4B,CAAvD,AAAA;IACA,IAAIkS,kBAAJ,EAAwBF,YAAY,GAAGA,YAAY,CAAC/T,MAAb,CAAqBkU,CAAAA,CAAD,GAAOA,CAAC,KAAKhU,WAAjC;IAAA,CAAf,CAAxB;IACA,MAAME,QAAQ,GAAG2T,YAAY,CAAC5T,IAAb,CAAmBF,CAAAA,IAAD,GACjCA,IAAI,CAAC8O,SAAL,CAAeoF,WAAf,EAAA,CAA6BC,UAA7B,CAAwCP,gBAAgB,CAACM,WAAjB,EAAxC,CADe;IAAA,CAAjB,AAAA;IAGA,OAAO/T,QAAQ,KAAKF,WAAb,GAA2BE,QAA3B,GAAsCpB,SAA7C,CAAA;CACD;AAED;;;GAGA,CACA,SAASgV,+BAAT,CAAsBK,KAAtB,EAAkCC,UAAlC,EAAsD;IACpD,OAAOD,KAAK,CAAC/V,GAAN,CAAU,CAACiW,CAAD,EAAIC,KAAJ,GAAcH,KAAK,CAAC,AAACC,CAAAA,UAAU,GAAGE,KAAd,CAAA,GAAuBH,KAAK,CAACtS,MAA9B,CAA7B;IAAA,CAAP,CAAA;CACD;AAED,MAAMtJ,yCAAI,GAAGhB,yCAAb,AAAA;AACA,MAAMiB,yCAAO,GAAGhB,wCAAhB,AAAA;AACA,MAAMiB,yCAAK,GAAGhB,yCAAd,AAAA;AACA,MAAMiB,yCAAI,GAAGhB,yCAAb,AAAA;AACA,MAAMiB,yCAAM,GAAGhB,yCAAf,AAAA;AACA,MAAMiB,yCAAO,GAAGhB,yCAAhB,AAAA;AACA,MAAMiB,wCAAQ,GAAGhB,yCAAjB,AAAA;AACA,MAAMiB,yCAAK,GAAGhB,yCAAd,AAAA;AACA,MAAMiB,yCAAK,GAAGhB,yCAAd,AAAA;AACA,MAAMiB,yCAAI,GAAGhB,yCAAb,AAAA;AACA,MAAMiB,yCAAQ,GAAGhB,yCAAjB,AAAA;AACA,MAAMiB,yCAAa,GAAGhB,yCAAtB,AAAA;AACA,MAAMiB,yCAAc,GAAGhB,yCAAvB,AAAA;AACA,MAAMiB,yCAAgB,GAAGhB,wCAAzB,AAAA;AACA,MAAMiB,yCAAS,GAAGhB,wCAAlB,AAAA;AACA,MAAMiB,yCAAK,GAAGhB,yCAAd,AAAA;;ADxoDA", "sources": ["packages/react/select/src/index.ts", "packages/react/select/src/Select.tsx"], "sourcesContent": ["export {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n} from './Select';\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n} from './Select';\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Slot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { VisuallyHidden } from '@radix-ui/react-visually-hidden';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst OPEN_KEYS = [' ', 'Enter', 'ArrowUp', 'ArrowDown'];\nconst SELECTION_KEYS = [' ', 'Enter'];\n\n/* -------------------------------------------------------------------------------------------------\n * Select\n * -----------------------------------------------------------------------------------------------*/\n\nconst SELECT_NAME = 'Select';\n\ntype ItemData = { value: string; disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  SelectItemElement,\n  ItemData\n>(SELECT_NAME);\n\ntype ScopedProps<P> = P & { __scopeSelect?: Scope };\nconst [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype SelectContextValue = {\n  trigger: SelectTriggerElement | null;\n  onTriggerChange(node: SelectTriggerElement | null): void;\n  valueNode: SelectValueElement | null;\n  onValueNodeChange(node: SelectValueElement): void;\n  valueNodeHasChildren: boolean;\n  onValueNodeHasChildrenChange(hasChildren: boolean): void;\n  contentId: string;\n  value?: string;\n  onValueChange(value: string): void;\n  open: boolean;\n  required?: boolean;\n  onOpenChange(open: boolean): void;\n  dir: SelectProps['dir'];\n  triggerPointerDownPosRef: React.MutableRefObject<{ x: number; y: number } | null>;\n  disabled?: boolean;\n};\n\nconst [SelectProvider, useSelectContext] = createSelectContext<SelectContextValue>(SELECT_NAME);\n\ntype NativeOption = React.ReactElement<React.ComponentProps<'option'>>;\n\ntype SelectNativeOptionsContextValue = {\n  onNativeOptionAdd(option: NativeOption): void;\n  onNativeOptionRemove(option: NativeOption): void;\n};\nconst [SelectNativeOptionsProvider, useSelectNativeOptionsContext] =\n  createSelectContext<SelectNativeOptionsContextValue>(SELECT_NAME);\n\ninterface SelectProps {\n  children?: React.ReactNode;\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  name?: string;\n  autoComplete?: string;\n  disabled?: boolean;\n  required?: boolean;\n}\n\nconst Select: React.FC<SelectProps> = (props: ScopedProps<SelectProps>) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState<SelectTriggerElement | null>(null);\n  const [valueNode, setValueNode] = React.useState<SelectValueElement | null>(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange,\n  });\n  const triggerPointerDownPosRef = React.useRef<{ x: number; y: number } | null>(null);\n\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  const isFormControl = trigger ? Boolean(trigger.closest('form')) : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(new Set<NativeOption>());\n\n  // The native `select` only associates the correct default value if the corresponding\n  // `option` is rendered as a child **at the same time** as itself.\n  // Because it might take a few renders for our items to gather the information to build\n  // the native `option`(s), we generate a key on the `select` to make sure React re-builds it\n  // each time the options change.\n  const nativeSelectKey = Array.from(nativeOptionsSet)\n    .map((option) => option.props.value)\n    .join(';');\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <SelectProvider\n        required={required}\n        scope={__scopeSelect}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        valueNode={valueNode}\n        onValueNodeChange={setValueNode}\n        valueNodeHasChildren={valueNodeHasChildren}\n        onValueNodeHasChildrenChange={setValueNodeHasChildren}\n        contentId={useId()}\n        value={value}\n        onValueChange={setValue}\n        open={open}\n        onOpenChange={setOpen}\n        dir={direction}\n        triggerPointerDownPosRef={triggerPointerDownPosRef}\n        disabled={disabled}\n      >\n        <Collection.Provider scope={__scopeSelect}>\n          <SelectNativeOptionsProvider\n            scope={props.__scopeSelect}\n            onNativeOptionAdd={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, [])}\n            onNativeOptionRemove={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, [])}\n          >\n            {children}\n          </SelectNativeOptionsProvider>\n        </Collection.Provider>\n\n        {isFormControl ? (\n          <BubbleSelect\n            key={nativeSelectKey}\n            aria-hidden\n            required={required}\n            tabIndex={-1}\n            name={name}\n            autoComplete={autoComplete}\n            value={value}\n            // enable form autofill\n            onChange={(event) => setValue(event.target.value)}\n            disabled={disabled}\n          >\n            {value === undefined ? <option value=\"\" /> : null}\n            {Array.from(nativeOptionsSet)}\n          </BubbleSelect>\n        ) : null}\n      </SelectProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nSelect.displayName = SELECT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'SelectTrigger';\n\ntype SelectTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = Radix.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SelectTriggerProps extends PrimitiveButtonProps {}\n\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props: ScopedProps<SelectTriggerProps>, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== undefined) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n\n    const handleOpen = () => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        // reset typeahead when we open\n        resetTypeahead();\n      }\n    };\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          type=\"button\"\n          role=\"combobox\"\n          aria-controls={context.contentId}\n          aria-expanded={context.open}\n          aria-required={context.required}\n          aria-autocomplete=\"none\"\n          dir={context.dir}\n          data-state={context.open ? 'open' : 'closed'}\n          disabled={isDisabled}\n          data-disabled={isDisabled ? '' : undefined}\n          data-placeholder={context.value === undefined ? '' : undefined}\n          {...triggerProps}\n          ref={composedRefs}\n          // Enable compatibility with native label or custom `Label` \"click\" for Safari:\n          onClick={composeEventHandlers(triggerProps.onClick, (event) => {\n            // Whilst browsers generally have no issue focusing the trigger when clicking\n            // on a label, Safari seems to struggle with the fact that there's no `onClick`.\n            // We force `focus` in this case. Note: this doesn't create any other side-effect\n            // because we are preventing default in `onPointerDown` so effectively\n            // this only runs for a label \"click\"\n            event.currentTarget.focus();\n          })}\n          onPointerDown={composeEventHandlers(triggerProps.onPointerDown, (event) => {\n            // prevent implicit pointer capture\n            // https://www.w3.org/TR/pointerevents3/#implicit-pointer-capture\n            const target = event.target as HTMLElement;\n            if (target.hasPointerCapture(event.pointerId)) {\n              target.releasePointerCapture(event.pointerId);\n            }\n\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (event.button === 0 && event.ctrlKey === false) {\n              handleOpen();\n              context.triggerPointerDownPosRef.current = {\n                x: Math.round(event.pageX),\n                y: Math.round(event.pageY),\n              };\n              // prevent trigger from stealing focus from the active item after opening.\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(triggerProps.onKeyDown, (event) => {\n            const isTypingAhead = searchRef.current !== '';\n            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n            if (isTypingAhead && event.key === ' ') return;\n            if (OPEN_KEYS.includes(event.key)) {\n              handleOpen();\n              event.preventDefault();\n            }\n          })}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nSelectTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectValue\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALUE_NAME = 'SelectValue';\n\ntype SelectValueElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = Radix.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SelectValueProps extends Omit<PrimitiveSpanProps, 'placeholder'> {\n  placeholder?: React.ReactNode;\n}\n\nconst SelectValue = React.forwardRef<SelectValueElement, SelectValueProps>(\n  (props: ScopedProps<SelectValueProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, children, placeholder, ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== undefined;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n\n    return (\n      <Primitive.span\n        {...valueProps}\n        ref={composedRefs}\n        // we don't want events from the portalled `SelectValue` children to bubble\n        // through the item they came from\n        style={{ pointerEvents: 'none' }}\n      >\n        {context.value === undefined && placeholder !== undefined ? placeholder : children}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectValue.displayName = VALUE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst ICON_NAME = 'SelectIcon';\n\ntype SelectIconElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectIconProps extends PrimitiveSpanProps {}\n\nconst SelectIcon = React.forwardRef<SelectIconElement, SelectIconProps>(\n  (props: ScopedProps<SelectIconProps>, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return (\n      <Primitive.span aria-hidden {...iconProps} ref={forwardedRef}>\n        {children || '▼'}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectIcon.displayName = ICON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'SelectPortal';\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface SelectPortalProps extends Omit<PortalProps, 'asChild'> {\n  children?: React.ReactNode;\n}\n\nconst SelectPortal: React.FC<SelectPortalProps> = (props: ScopedProps<SelectPortalProps>) => {\n  return <PortalPrimitive asChild {...props} />;\n};\n\nSelectPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'SelectContent';\n\ntype SelectContentElement = SelectContentImplElement;\ninterface SelectContentProps extends SelectContentImplProps {}\n\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props: ScopedProps<SelectContentProps>, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState<DocumentFragment>();\n\n    // setting the fragment in `useLayoutEffect` as `DocumentFragment` doesn't exist on the server\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n\n    if (!context.open) {\n      const frag = fragment as Element | undefined;\n      return frag\n        ? ReactDOM.createPortal(\n            <SelectContentProvider scope={props.__scopeSelect}>\n              <Collection.Slot scope={props.__scopeSelect}>\n                <div>{props.children}</div>\n              </Collection.Slot>\n            </SelectContentProvider>,\n            frag\n          )\n        : null;\n    }\n\n    return <SelectContentImpl {...props} ref={forwardedRef} />;\n  }\n);\n\nSelectContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContentImpl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_MARGIN = 10;\n\ntype SelectContentContextValue = {\n  content?: SelectContentElement | null;\n  viewport?: SelectViewportElement | null;\n  onViewportChange?: (node: SelectViewportElement | null) => void;\n  itemRefCallback?: (node: SelectItemElement | null, value: string, disabled: boolean) => void;\n  selectedItem?: SelectItemElement | null;\n  onItemLeave?: () => void;\n  itemTextRefCallback?: (\n    node: SelectItemTextElement | null,\n    value: string,\n    disabled: boolean\n  ) => void;\n  focusSelectedItem?: () => void;\n  selectedItemText?: SelectItemTextElement | null;\n  position?: SelectContentProps['position'];\n  isPositioned?: boolean;\n  searchRef?: React.RefObject<string>;\n};\n\nconst [SelectContentProvider, useSelectContentContext] =\n  createSelectContext<SelectContentContextValue>(CONTENT_NAME);\n\nconst CONTENT_IMPL_NAME = 'SelectContentImpl';\n\ntype SelectContentImplElement = SelectPopperPositionElement | SelectItemAlignedPositionElement;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = Radix.ComponentPropsWithoutRef<typeof FocusScope>;\n\ntype SelectPopperPrivateProps = { onPlaced?: PopperContentProps['onPlaced'] };\n\ninterface SelectContentImplProps\n  extends Omit<SelectPopperPositionProps, keyof SelectPopperPrivateProps>,\n    Omit<SelectItemAlignedPositionProps, keyof SelectPopperPrivateProps> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n\n  position?: 'item-aligned' | 'popper';\n}\n\nconst SelectContentImpl = React.forwardRef<SelectContentImplElement, SelectContentImplProps>(\n  (props: ScopedProps<SelectContentImplProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = 'item-aligned',\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState<SelectContentImplElement | null>(null);\n    const [viewport, setViewport] = React.useState<SelectViewportElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState<SelectItemElement | null>(null);\n    const [selectedItemText, setSelectedItemText] = React.useState<SelectItemTextElement | null>(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n\n    // Make sure the whole tree has focus guards as our `Select` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const focusFirst = React.useCallback(\n      (candidates: Array<HTMLElement | null>) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          // if focus is already where we want to go, we don't want to keep going through the candidates\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: 'nearest' });\n          // viewport might have padding so scroll to its edges when focusing first/last items.\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n\n    // Since this is not dependent on layout, we want to ensure this runs at the same time as\n    // other effects across components. Hence why we don't call `focusSelectedItem` inside `position`.\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n\n    // prevent selecting items on `pointerup` in some cases after opening from `pointerdown`\n    // and close on `pointerup` outside.\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n\n        const handlePointerMove = (event: PointerEvent) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0)),\n          };\n        };\n        const handlePointerUp = (event: PointerEvent) => {\n          // If the pointer hasn't moved by a certain threshold then we prevent selecting item on `pointerup`.\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            // otherwise, if the event was outside the content, close.\n            if (!content.contains(event.target as HTMLElement)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener('pointermove', handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener('pointermove', handlePointerMove);\n          document.addEventListener('pointerup', handlePointerUp, { capture: true, once: true });\n        }\n\n        return () => {\n          document.removeEventListener('pointermove', handlePointerMove);\n          document.removeEventListener('pointerup', handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener('blur', close);\n      window.addEventListener('resize', close);\n      return () => {\n        window.removeEventListener('blur', close);\n        window.removeEventListener('resize', close);\n      };\n    }, [onOpenChange]);\n\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (nextItem.ref.current as HTMLElement).focus());\n      }\n    });\n\n    const itemRefCallback = React.useCallback(\n      (node: SelectItemElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node: SelectItemTextElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n\n    const SelectPosition = position === 'popper' ? SelectPopperPosition : SelectItemAlignedPosition;\n\n    // Silently ignore props that are not supported by `SelectItemAlignedPosition`\n    const popperContentProps =\n      SelectPosition === SelectPopperPosition\n        ? {\n            side,\n            sideOffset,\n            align,\n            alignOffset,\n            arrowPadding,\n            collisionBoundary,\n            collisionPadding,\n            sticky,\n            hideWhenDetached,\n            avoidCollisions,\n          }\n        : {};\n\n    return (\n      <SelectContentProvider\n        scope={__scopeSelect}\n        content={content}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        itemRefCallback={itemRefCallback}\n        selectedItem={selectedItem}\n        onItemLeave={handleItemLeave}\n        itemTextRefCallback={itemTextRefCallback}\n        focusSelectedItem={focusSelectedItem}\n        selectedItemText={selectedItemText}\n        position={position}\n        isPositioned={isPositioned}\n        searchRef={searchRef}\n      >\n        <RemoveScroll as={Slot} allowPinchZoom>\n          <FocusScope\n            asChild\n            // we make sure we're not trapping once it's been closed\n            // (closed !== unmounted when animating out)\n            trapped={context.open}\n            onMountAutoFocus={(event) => {\n              // we prevent open autofocus because we manually focus the selected item\n              event.preventDefault();\n            }}\n            onUnmountAutoFocus={composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            })}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              // When focus is trapped, a focusout event may still happen.\n              // We make sure we don't trigger our `onDismiss` in such case.\n              onFocusOutside={(event) => event.preventDefault()}\n              onDismiss={() => context.onOpenChange(false)}\n            >\n              <SelectPosition\n                role=\"listbox\"\n                id={context.contentId}\n                data-state={context.open ? 'open' : 'closed'}\n                dir={context.dir}\n                onContextMenu={(event) => event.preventDefault()}\n                {...contentProps}\n                {...popperContentProps}\n                onPlaced={() => setIsPositioned(true)}\n                ref={composedRefs}\n                style={{\n                  // flex layout so we can place the scroll buttons properly\n                  display: 'flex',\n                  flexDirection: 'column',\n                  // reset the outline by default as the content MAY get focused\n                  outline: 'none',\n                  ...contentProps.style,\n                }}\n                onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                  const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n\n                  // select should not be navigated using tab key so we prevent it\n                  if (event.key === 'Tab') event.preventDefault();\n\n                  if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n\n                  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n                    const items = getItems().filter((item) => !item.disabled);\n                    let candidateNodes = items.map((item) => item.ref.current!);\n\n                    if (['ArrowUp', 'End'].includes(event.key)) {\n                      candidateNodes = candidateNodes.slice().reverse();\n                    }\n                    if (['ArrowUp', 'ArrowDown'].includes(event.key)) {\n                      const currentElement = event.target as SelectItemElement;\n                      const currentIndex = candidateNodes.indexOf(currentElement);\n                      candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n\n                    /**\n                     * Imperative focus during keydown is risky so we prevent React's batching updates\n                     * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n                     */\n                    setTimeout(() => focusFirst(candidateNodes));\n\n                    event.preventDefault();\n                  }\n                })}\n              />\n            </DismissableLayer>\n          </FocusScope>\n        </RemoveScroll>\n      </SelectContentProvider>\n    );\n  }\n);\n\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemAlignedPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_ALIGNED_POSITION_NAME = 'SelectItemAlignedPosition';\n\ntype SelectItemAlignedPositionElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectItemAlignedPositionProps extends PrimitiveDivProps, SelectPopperPrivateProps {}\n\nconst SelectItemAlignedPosition = React.forwardRef<\n  SelectItemAlignedPositionElement,\n  SelectItemAlignedPositionProps\n>((props: ScopedProps<SelectItemAlignedPositionProps>, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState<HTMLDivElement | null>(null);\n  const [content, setContent] = React.useState<SelectItemAlignedPositionElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (\n      context.trigger &&\n      context.valueNode &&\n      contentWrapper &&\n      content &&\n      viewport &&\n      selectedItem &&\n      selectedItemText\n    ) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n\n      // -----------------------------------------------------------------------------------------\n      //  Horizontal positioning\n      // -----------------------------------------------------------------------------------------\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n\n      if (context.dir !== 'rtl') {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [CONTENT_MARGIN, rightEdge - contentWidth]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.left = clampedLeft + 'px';\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [CONTENT_MARGIN, leftEdge - contentWidth]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.right = clampedRight + 'px';\n      }\n\n      // -----------------------------------------------------------------------------------------\n      // Vertical positioning\n      // -----------------------------------------------------------------------------------------\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth; // prettier-ignore\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem = selectedItem === items[items.length - 1].ref.current;\n        contentWrapper.style.bottom = 0 + 'px';\n        const viewportOffsetBottom =\n          content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight +\n            // viewport might have padding bottom, include it to avoid a scrollable viewport\n            (isLastItem ? viewportPaddingBottom : 0) +\n            viewportOffsetBottom +\n            contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + 'px';\n      } else {\n        const isFirstItem = selectedItem === items[0].ref.current;\n        contentWrapper.style.top = 0 + 'px';\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth +\n            viewport.offsetTop +\n            // viewport might have padding top, include it to avoid a scrollable viewport\n            (isFirstItem ? viewportPaddingTop : 0) +\n            selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + 'px';\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + 'px';\n      contentWrapper.style.maxHeight = availableHeight + 'px';\n      // -----------------------------------------------------------------------------------------\n\n      onPlaced?.();\n\n      // we don't want the initial scroll position adjustment to trigger \"expand on scroll\"\n      // so we explicitly turn it on only after they've registered.\n      requestAnimationFrame(() => (shouldExpandOnScrollRef.current = true));\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced,\n  ]);\n\n  useLayoutEffect(() => position(), [position]);\n\n  // copy z-index from content to wrapper\n  const [contentZIndex, setContentZIndex] = React.useState<string>();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n\n  // When the viewport becomes scrollable at the top, the scroll up button will mount.\n  // Because it is part of the normal flow, it will push down the viewport, thus throwing our\n  // trigger => selectedItem alignment off by the amount the viewport was pushed down.\n  // We wait for this to happen and then re-run the positining logic one more time to account for it.\n  const handleScrollButtonChange = React.useCallback(\n    (node: SelectScrollButtonImplElement | null) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n\n  return (\n    <SelectViewportProvider\n      scope={__scopeSelect}\n      contentWrapper={contentWrapper}\n      shouldExpandOnScrollRef={shouldExpandOnScrollRef}\n      onScrollButtonChange={handleScrollButtonChange}\n    >\n      <div\n        ref={setContentWrapper}\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'fixed',\n          zIndex: contentZIndex,\n        }}\n      >\n        <Primitive.div\n          {...popperProps}\n          ref={composedRefs}\n          style={{\n            // When we get the height of the content, it includes borders. If we were to set\n            // the height without having `boxSizing: 'border-box'` it would be too big.\n            boxSizing: 'border-box',\n            // We need to ensure the content doesn't get taller than the wrapper\n            maxHeight: '100%',\n            ...popperProps.style,\n          }}\n        />\n      </div>\n    </SelectViewportProvider>\n  );\n});\n\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPopperPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_POSITION_NAME = 'SelectPopperPosition';\n\ntype SelectPopperPositionElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface SelectPopperPositionProps extends PopperContentProps, SelectPopperPrivateProps {}\n\nconst SelectPopperPosition = React.forwardRef<\n  SelectPopperPositionElement,\n  SelectPopperPositionProps\n>((props: ScopedProps<SelectPopperPositionProps>, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = 'start',\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n\n  return (\n    <PopperPrimitive.Content\n      {...popperScope}\n      {...popperProps}\n      ref={forwardedRef}\n      align={align}\n      collisionPadding={collisionPadding}\n      style={{\n        // Ensure border-box for floating-ui calculations\n        boxSizing: 'border-box',\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-select-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-select-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-select-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-select-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-select-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectViewport\n * -----------------------------------------------------------------------------------------------*/\n\ntype SelectViewportContextValue = {\n  contentWrapper?: HTMLDivElement | null;\n  shouldExpandOnScrollRef?: React.RefObject<boolean>;\n  onScrollButtonChange?: (node: SelectScrollButtonImplElement | null) => void;\n};\n\nconst [SelectViewportProvider, useSelectViewportContext] =\n  createSelectContext<SelectViewportContextValue>(CONTENT_NAME, {});\n\nconst VIEWPORT_NAME = 'SelectViewport';\n\ntype SelectViewportElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SelectViewportProps extends PrimitiveDivProps {}\n\nconst SelectViewport = React.forwardRef<SelectViewportElement, SelectViewportProps>(\n  (props: ScopedProps<SelectViewportProps>, forwardedRef) => {\n    const { __scopeSelect, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n        />\n        <Collection.Slot scope={__scopeSelect}>\n          <Primitive.div\n            data-radix-select-viewport=\"\"\n            role=\"presentation\"\n            {...viewportProps}\n            ref={composedRefs}\n            style={{\n              // we use position: 'relative' here on the `viewport` so that when we call\n              // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n              // (independent of the scrollUpButton).\n              position: 'relative',\n              flex: 1,\n              overflow: 'auto',\n              ...viewportProps.style,\n            }}\n            onScroll={composeEventHandlers(viewportProps.onScroll, (event) => {\n              const viewport = event.currentTarget;\n              const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n              if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                if (scrolledBy > 0) {\n                  const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                  const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                  const cssHeight = parseFloat(contentWrapper.style.height);\n                  const prevHeight = Math.max(cssMinHeight, cssHeight);\n\n                  if (prevHeight < availableHeight) {\n                    const nextHeight = prevHeight + scrolledBy;\n                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                    const heightDiff = nextHeight - clampedNextHeight;\n\n                    contentWrapper.style.height = clampedNextHeight + 'px';\n                    if (contentWrapper.style.bottom === '0px') {\n                      viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                      // ensure the content stays pinned to the bottom\n                      contentWrapper.style.justifyContent = 'flex-end';\n                    }\n                  }\n                }\n              }\n              prevScrollTopRef.current = viewport.scrollTop;\n            })}\n          />\n        </Collection.Slot>\n      </>\n    );\n  }\n);\n\nSelectViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'SelectGroup';\n\ntype SelectGroupContextValue = { id: string };\n\nconst [SelectGroupContextProvider, useSelectGroupContext] =\n  createSelectContext<SelectGroupContextValue>(GROUP_NAME);\n\ntype SelectGroupElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectGroupProps extends PrimitiveDivProps {}\n\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  (props: ScopedProps<SelectGroupProps>, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return (\n      <SelectGroupContextProvider scope={__scopeSelect} id={groupId}>\n        <Primitive.div role=\"group\" aria-labelledby={groupId} {...groupProps} ref={forwardedRef} />\n      </SelectGroupContextProvider>\n    );\n  }\n);\n\nSelectGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'SelectLabel';\n\ntype SelectLabelElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectLabelProps extends PrimitiveDivProps {}\n\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  (props: ScopedProps<SelectLabelProps>, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return <Primitive.div id={groupContext.id} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nSelectLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'SelectItem';\n\ntype SelectItemContextValue = {\n  value: string;\n  disabled: boolean;\n  textId: string;\n  isSelected: boolean;\n  onItemTextChange(node: SelectItemTextElement | null): void;\n};\n\nconst [SelectItemContextProvider, useSelectItemContext] =\n  createSelectContext<SelectItemContextValue>(ITEM_NAME);\n\ntype SelectItemElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectItemProps extends PrimitiveDivProps {\n  value: string;\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>(\n  (props: ScopedProps<SelectItemProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? '');\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) =>\n      contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n\n    return (\n      <SelectItemContextProvider\n        scope={__scopeSelect}\n        value={value}\n        disabled={disabled}\n        textId={textId}\n        isSelected={isSelected}\n        onItemTextChange={React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? '').trim());\n        }, [])}\n      >\n        <Collection.ItemSlot\n          scope={__scopeSelect}\n          value={value}\n          disabled={disabled}\n          textValue={textValue}\n        >\n          <Primitive.div\n            role=\"option\"\n            aria-labelledby={textId}\n            data-highlighted={isFocused ? '' : undefined}\n            // `isFocused` caveat fixes stuttering in VoiceOver\n            aria-selected={isSelected && isFocused}\n            data-state={isSelected ? 'checked' : 'unchecked'}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            tabIndex={disabled ? undefined : -1}\n            {...itemProps}\n            ref={composedRefs}\n            onFocus={composeEventHandlers(itemProps.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(itemProps.onBlur, () => setIsFocused(false))}\n            onPointerUp={composeEventHandlers(itemProps.onPointerUp, handleSelect)}\n            onPointerMove={composeEventHandlers(itemProps.onPointerMove, (event) => {\n              if (disabled) {\n                contentContext.onItemLeave?.();\n              } else {\n                // even though safari doesn't support this option, it's acceptable\n                // as it only means it might scroll a few pixels when using the pointer.\n                event.currentTarget.focus({ preventScroll: true });\n              }\n            })}\n            onPointerLeave={composeEventHandlers(itemProps.onPointerLeave, (event) => {\n              if (event.currentTarget === document.activeElement) {\n                contentContext.onItemLeave?.();\n              }\n            })}\n            onKeyDown={composeEventHandlers(itemProps.onKeyDown, (event) => {\n              const isTypingAhead = contentContext.searchRef?.current !== '';\n              if (isTypingAhead && event.key === ' ') return;\n              if (SELECTION_KEYS.includes(event.key)) handleSelect();\n              // prevent page scroll if using the space key to select an item\n              if (event.key === ' ') event.preventDefault();\n            })}\n          />\n        </Collection.ItemSlot>\n      </SelectItemContextProvider>\n    );\n  }\n);\n\nSelectItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemText\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_TEXT_NAME = 'SelectItemText';\n\ntype SelectItemTextElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectItemTextProps extends PrimitiveSpanProps {}\n\nconst SelectItemText = React.forwardRef<SelectItemTextElement, SelectItemTextProps>(\n  (props: ScopedProps<SelectItemTextProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState<SelectItemTextElement | null>(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => (\n        <option key={itemContext.value} value={itemContext.value} disabled={itemContext.disabled}>\n          {textContent}\n        </option>\n      ),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n\n    return (\n      <>\n        <Primitive.span id={itemContext.textId} {...itemTextProps} ref={composedRefs} />\n\n        {/* Portal the select item text into the trigger value node */}\n        {itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren\n          ? ReactDOM.createPortal(itemTextProps.children, context.valueNode)\n          : null}\n      </>\n    );\n  }\n);\n\nSelectItemText.displayName = ITEM_TEXT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'SelectItemIndicator';\n\ntype SelectItemIndicatorElement = React.ElementRef<typeof Primitive.span>;\ninterface SelectItemIndicatorProps extends PrimitiveSpanProps {}\n\nconst SelectItemIndicator = React.forwardRef<SelectItemIndicatorElement, SelectItemIndicatorProps>(\n  (props: ScopedProps<SelectItemIndicatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? (\n      <Primitive.span aria-hidden {...itemIndicatorProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollUpButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_UP_BUTTON_NAME = 'SelectScrollUpButton';\n\ntype SelectScrollUpButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollUpButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollUpButton = React.forwardRef<\n  SelectScrollUpButtonElement,\n  SelectScrollUpButtonProps\n>((props: ScopedProps<SelectScrollUpButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const canScrollUp = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollUp ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollDownButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_DOWN_BUTTON_NAME = 'SelectScrollDownButton';\n\ntype SelectScrollDownButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollDownButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollDownButton = React.forwardRef<\n  SelectScrollDownButtonElement,\n  SelectScrollDownButtonProps\n>((props: ScopedProps<SelectScrollDownButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        // we use Math.ceil here because if the UI is zoomed-in\n        // `scrollTop` is not always reported as an integer\n        const canScrollDown = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollDown ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\n\ntype SelectScrollButtonImplElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectScrollButtonImplProps extends PrimitiveDivProps {\n  onAutoScroll(): void;\n}\n\nconst SelectScrollButtonImpl = React.forwardRef<\n  SelectScrollButtonImplElement,\n  SelectScrollButtonImplProps\n>((props: ScopedProps<SelectScrollButtonImplProps>, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext('SelectScrollButton', __scopeSelect);\n  const autoScrollTimerRef = React.useRef<number | null>(null);\n  const getItems = useCollection(__scopeSelect);\n\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n\n  // When the viewport becomes scrollable on either side, the relevant scroll button will mount.\n  // Because it is part of the normal flow, it will push down (top button) or shrink (bottom button)\n  // the viewport, potentially causing the active item to now be partially out of view.\n  // We re-run the `scrollIntoView` logic to make sure it stays within the viewport.\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: 'nearest' });\n  }, [getItems]);\n\n  return (\n    <Primitive.div\n      aria-hidden\n      {...scrollIndicatorProps}\n      ref={forwardedRef}\n      style={{ flexShrink: 0, ...scrollIndicatorProps.style }}\n      onPointerDown={composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerMove={composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerLeave={composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })}\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * SelectSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'SelectSeparator';\n\ntype SelectSeparatorElement = React.ElementRef<typeof Primitive.div>;\ninterface SelectSeparatorProps extends PrimitiveDivProps {}\n\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  (props: ScopedProps<SelectSeparatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return <Primitive.div aria-hidden {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nSelectSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'SelectArrow';\n\ntype SelectArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = Radix.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface SelectArrowProps extends PopperArrowProps {}\n\nconst SelectArrow = React.forwardRef<SelectArrowElement, SelectArrowProps>(\n  (props: ScopedProps<SelectArrowProps>, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === 'popper' ? (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst BubbleSelect = React.forwardRef<HTMLSelectElement, React.ComponentPropsWithoutRef<'select'>>(\n  (props, forwardedRef) => {\n    const { value, ...selectProps } = props;\n    const ref = React.useRef<HTMLSelectElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const select = ref.current!;\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        'value'\n      ) as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('change', { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully use a `select` here to support form autofill as much\n     * as possible.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programatically and bubble to any parent form `onChange` event.\n     * Adding the `value` will cause React to consider the programatic\n     * dispatch a duplicate and it will get swallowed.\n     *\n     * We use `VisuallyHidden` rather than `display: \"none\"` because Safari autofill\n     * won't work otherwise.\n     */\n    return (\n      <VisuallyHidden asChild>\n        <select {...selectProps} ref={composedRefs} defaultValue={value} />\n      </VisuallyHidden>\n    );\n  }\n);\n\nBubbleSelect.displayName = 'BubbleSelect';\n\nfunction useTypeaheadSearch(onSearchChange: (search: string) => void) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef('');\n  const timerRef = React.useRef(0);\n\n  const handleTypeaheadSearch = React.useCallback(\n    (key: string) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        // Reset `searchRef` 1 second after it was last updated\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = '';\n    window.clearTimeout(timerRef.current);\n  }, []);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n\n  return [searchRef, handleTypeaheadSearch, resetTypeahead] as const;\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in a list of items,\n * the search and the current item, and returns the next item (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through items starting with that character)\n *\n * We also reorder the items by wrapping the array around the current item.\n * This is so we always look forward from the current item, and picking the first\n * item will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current item from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current item still matches.\n */\nfunction findNextItem<T extends { textValue: string }>(\n  items: T[],\n  search: string,\n  currentItem?: T\n) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find((item) =>\n    item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : undefined;\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\n\nconst Root = Select;\nconst Trigger = SelectTrigger;\nconst Value = SelectValue;\nconst Icon = SelectIcon;\nconst Portal = SelectPortal;\nconst Content = SelectContent;\nconst Viewport = SelectViewport;\nconst Group = SelectGroup;\nconst Label = SelectLabel;\nconst Item = SelectItem;\nconst ItemText = SelectItemText;\nconst ItemIndicator = SelectItemIndicator;\nconst ScrollUpButton = SelectScrollUpButton;\nconst ScrollDownButton = SelectScrollDownButton;\nconst Separator = SelectSeparator;\nconst Arrow = SelectArrow;\n\nexport {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n};\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n};\n"], "names": ["createSelectScope", "Select", "SelectTrigger", "SelectValue", "SelectIcon", "SelectPortal", "SelectContent", "SelectViewport", "SelectGroup", "SelectLabel", "SelectItem", "SelectItemText", "SelectItemIndicator", "SelectScrollUpButton", "SelectScrollDownButton", "SelectSeparator", "SelectArrow", "Root", "<PERSON><PERSON>", "Value", "Icon", "Portal", "Content", "Viewport", "Group", "Label", "<PERSON><PERSON>", "ItemText", "ItemIndicator", "ScrollUpButton", "ScrollDownButton", "Separator", "Arrow", "React", "ReactDOM", "clamp", "composeEventHandlers", "createCollection", "useComposedRefs", "createContextScope", "useDirection", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "useFocusGuards", "FocusScope", "useId", "PopperPrimitive", "createPopperScope", "PortalPrimitive", "Primitive", "Slot", "useCallbackRef", "useControllableState", "useLayoutEffect", "usePrevious", "VisuallyHidden", "hideOthers", "RemoveScroll", "OPEN_KEYS", "SELECTION_KEYS", "SELECT_NAME", "Collection", "useCollection", "createCollectionScope", "createSelectContext", "usePopperScope", "SelectProvider", "useSelectContext", "SelectNativeOptionsProvider", "useSelectNativeOptionsContext", "props", "__scopeSelect", "children", "open", "openProp", "defaultOpen", "onOpenChange", "value", "valueProp", "defaultValue", "onValueChange", "dir", "name", "autoComplete", "disabled", "required", "popperScope", "trigger", "setTrigger", "useState", "valueNode", "setValueNode", "valueNodeHasChildren", "setValueNodeHasChildren", "direction", "<PERSON><PERSON><PERSON>", "prop", "defaultProp", "onChange", "setValue", "triggerPointerDownPosRef", "useRef", "isFormControl", "Boolean", "closest", "nativeOptionsSet", "setNativeOptionsSet", "Set", "nativeSelectKey", "Array", "from", "map", "option", "join", "useCallback", "prev", "add", "optionsSet", "delete", "event", "target", "undefined", "TRIGGER_NAME", "forwardRef", "forwardedRef", "triggerProps", "context", "isDisabled", "composedRefs", "onTriggerChange", "getItems", "searchRef", "handleTypeaheadSearch", "resetTypeahead", "useTypeaheadSearch", "search", "enabledItems", "filter", "item", "currentItem", "find", "nextItem", "findNextItem", "handleOpen", "contentId", "onClick", "currentTarget", "focus", "onPointerDown", "hasPointerCapture", "pointerId", "releasePointerCapture", "button", "ctrl<PERSON>ey", "current", "x", "Math", "round", "pageX", "y", "pageY", "preventDefault", "onKeyDown", "isTypingAhead", "isModifierKey", "altKey", "metaKey", "key", "length", "includes", "VALUE_NAME", "className", "style", "placeholder", "valueProps", "onValueNodeHasChildrenChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onValueNodeChange", "pointerEvents", "ICON_NAME", "iconProps", "PORTAL_NAME", "CONTENT_NAME", "fragment", "setFragment", "DocumentFragment", "frag", "createPortal", "CONTENT_MARGIN", "SelectContentProvider", "useSelectContentContext", "CONTENT_IMPL_NAME", "SelectContentImpl", "position", "onCloseAutoFocus", "onEscapeKeyDown", "onPointerDownOutside", "side", "sideOffset", "align", "alignOffset", "arrowPadding", "collisionBoundary", "collisionPadding", "sticky", "hideWhenDetached", "avoidCollisions", "contentProps", "content", "<PERSON><PERSON><PERSON><PERSON>", "viewport", "setViewport", "node", "selectedItem", "setSelectedItem", "selectedItemText", "setSelectedItemText", "isPositioned", "setIsPositioned", "firstValidItemFoundRef", "useEffect", "focusFirst", "candidates", "firstItem", "restItems", "ref", "lastItem", "slice", "PREVIOUSLY_FOCUSED_ELEMENT", "document", "activeElement", "candidate", "scrollIntoView", "block", "scrollTop", "scrollHeight", "focusSelectedItem", "pointerMoveDel<PERSON>", "handlePointerMove", "abs", "handlePointerUp", "contains", "removeEventListener", "addEventListener", "capture", "once", "close", "window", "setTimeout", "itemRefCallback", "isFirstValidItem", "isSelectedItem", "handleItemLeave", "itemTextRefCallback", "SelectPosition", "SelectPopperPosition", "SelectItemAlignedPosition", "popperContentProps", "preventScroll", "display", "flexDirection", "outline", "items", "candidateNodes", "reverse", "currentElement", "currentIndex", "indexOf", "ITEM_ALIGNED_POSITION_NAME", "onPlaced", "popperProps", "contentContext", "contentWrapper", "setContentWrapper", "shouldExpandOnScrollRef", "shouldRepositionRef", "triggerRect", "getBoundingClientRect", "contentRect", "valueNodeRect", "itemTextRect", "itemTextOffset", "left", "leftDelta", "minC<PERSON>nt<PERSON>id<PERSON>", "width", "contentWidth", "max", "rightEdge", "innerWidth", "clampedLeft", "min<PERSON><PERSON><PERSON>", "right", "<PERSON><PERSON><PERSON><PERSON>", "leftEdge", "clampedRight", "availableHeight", "innerHeight", "itemsHeight", "contentStyles", "getComputedStyle", "contentBorderTopWidth", "parseInt", "borderTopWidth", "contentPaddingTop", "paddingTop", "contentBorderBottomWidth", "borderBottomWidth", "contentPaddingBottom", "paddingBottom", "fullContentHeight", "minContentHeight", "min", "offsetHeight", "viewportStyles", "viewportPaddingTop", "viewportPaddingBottom", "topEdgeToTriggerMiddle", "top", "height", "triggerMiddleToBottomEdge", "selectedItemHalfHeight", "itemOffsetMiddle", "offsetTop", "contentTopToItemMiddle", "itemMiddleToContentBottom", "willAlignWithoutTopOverflow", "isLastItem", "bottom", "viewportOffsetBottom", "clientHeight", "clampedTriggerMiddleToBottomEdge", "isFirstItem", "clampedTopEdgeToTriggerMiddle", "margin", "minHeight", "maxHeight", "requestAnimationFrame", "contentZIndex", "setContentZIndex", "zIndex", "handleScrollButtonChange", "boxSizing", "POPPER_POSITION_NAME", "SelectViewportProvider", "useSelectViewportContext", "VIEWPORT_NAME", "viewportProps", "viewportContext", "onViewportChange", "prevScrollTopRef", "__html", "flex", "overflow", "onScroll", "scrolledBy", "cssMinHeight", "parseFloat", "cssHeight", "prevHeight", "nextHeight", "clampedNextHeight", "heightDiff", "justifyContent", "GROUP_NAME", "SelectGroupContextProvider", "useSelectGroupContext", "groupProps", "groupId", "LABEL_NAME", "labelProps", "groupContext", "id", "ITEM_NAME", "SelectItemContextProvider", "useSelectItemContext", "textValue", "textValueProp", "itemProps", "isSelected", "setTextValue", "isFocused", "setIsFocused", "textId", "handleSelect", "prevTextValue", "textContent", "trim", "onFocus", "onBlur", "onPointerUp", "onPointerMove", "onItemLeave", "onPointerLeave", "ITEM_TEXT_NAME", "itemTextProps", "itemContext", "nativeOptionsContext", "itemTextNode", "setItemTextNode", "onItemTextChange", "nativeOption", "useMemo", "onNativeOptionAdd", "onNativeOptionRemove", "ITEM_INDICATOR_NAME", "itemIndicatorProps", "SCROLL_UP_BUTTON_NAME", "canScrollUp", "setCanScrollUp", "onScrollButtonChange", "handleScroll", "SCROLL_DOWN_BUTTON_NAME", "canScrollDown", "setCanScrollDown", "maxScroll", "ceil", "SelectScrollButtonImpl", "onAutoScroll", "scrollIndicatorProps", "autoScrollTimerRef", "clearAutoScrollTimer", "clearInterval", "activeItem", "flexShrink", "setInterval", "SEPARATOR_NAME", "separatorProps", "ARROW_NAME", "arrowProps", "BubbleSelect", "selectProps", "prevValue", "select", "selectProto", "HTMLSelectElement", "prototype", "descriptor", "Object", "getOwnPropertyDescriptor", "set", "Event", "bubbles", "call", "dispatchEvent", "displayName", "onSearchChange", "handleSearchChange", "timerRef", "updateSearch", "clearTimeout", "isRepeated", "every", "char", "normalizedSearch", "currentItemIndex", "wrappedItems", "wrapArray", "excludeCurrentItem", "v", "toLowerCase", "startsWith", "array", "startIndex", "_", "index"], "version": 3, "file": "index.js.map"}