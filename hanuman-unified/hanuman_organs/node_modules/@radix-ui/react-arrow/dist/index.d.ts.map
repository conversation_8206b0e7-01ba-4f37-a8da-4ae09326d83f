{"mappings": ";;;AAYA,yBAAyB,MAAM,wBAAwB,CAAC,OAAO,UAAU,GAAG,CAAC,CAAC;AAC9E,2BAAqB,SAAQ,iBAAiB;CAAG;AAEjD,OAAA,MAAM,uFAeJ,CAAC;AAMH,OAAA,MAAM,sFAAY,CAAC", "sources": ["packages/react/arrow/src/packages/react/arrow/src/Arrow.tsx", "packages/react/arrow/src/packages/react/arrow/src/index.ts", "packages/react/arrow/src/index.ts"], "sourcesContent": [null, null, "export {\n  Arrow,\n  //\n  Root,\n} from './Arrow';\nexport type { ArrowProps } from './Arrow';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}