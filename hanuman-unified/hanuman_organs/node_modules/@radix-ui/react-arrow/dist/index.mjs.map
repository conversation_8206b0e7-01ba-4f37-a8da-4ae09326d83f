{"mappings": ";;;;A;;;ACKA;;oGAEA,CAEA,MAAMI,0BAAI,GAAG,OAAb,AAAA;AAMA,MAAMJ,yCAAK,GAAA,aAAGE,CAAAA,iBAAA,CAA2C,CAACI,KAAD,EAAQC,YAAR,GAAyB;IAChF,MAAM,E,UAAEC,QAAF,CAAA,SAAYC,KAAK,GAAG,EAApB,WAAwBC,MAAM,GAAG,CAAjC,GAAoC,GAAGC,UAAH,EAApC,GAAsDL,KAA5D,AAAM;IACN,OAAA,aACE,CAAA,oBAAA,CAAC,gBAAD,CAAW,GAAX,EAAA,oCAAA,CAAA,EAAA,EACMK,UADN,EADF;QAGI,GAAG,EAAEJ,YAFP;QAGE,KAAK,EAAEE,KAHT;QAIE,MAAM,EAAEC,MAJV;QAKE,OAAO,EAAC,WALV;QAME,mBAAmB,EAAC,MAApB;KANF,CAAA,EASGJ,KAAK,CAACM,OAAN,GAAgBJ,QAAhB,GAAA,aAA2B,CAAA,oBAT9B,CAAA,SAAA,EAAA;QASuC,MAAM,EAAC,gBAAP;KAAT,CAT9B,CADF,CAUgC;CAZpB,CAAd,AAeC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,0BAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAEA,MAAMP,yCAAI,GAAGD,yCAAb,AAAA;;ADpCA", "sources": ["packages/react/arrow/src/index.ts", "packages/react/arrow/src/Arrow.tsx"], "sourcesContent": ["export {\n  Arrow,\n  //\n  Root,\n} from './Arrow';\nexport type { ArrowProps } from './Arrow';\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Arrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Arrow';\n\ntype ArrowElement = React.ElementRef<typeof Primitive.svg>;\ntype PrimitiveSvgProps = Radix.ComponentPropsWithoutRef<typeof Primitive.svg>;\ninterface ArrowProps extends PrimitiveSvgProps {}\n\nconst Arrow = React.forwardRef<ArrowElement, ArrowProps>((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return (\n    <Primitive.svg\n      {...arrowProps}\n      ref={forwardedRef}\n      width={width}\n      height={height}\n      viewBox=\"0 0 30 10\"\n      preserveAspectRatio=\"none\"\n    >\n      {/* We use their children if they're slotting to replace the whole svg */}\n      {props.asChild ? children : <polygon points=\"0,0 30,0 15,10\" />}\n    </Primitive.svg>\n  );\n});\n\nArrow.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Arrow;\n\nexport {\n  Arrow,\n  //\n  Root,\n};\nexport type { ArrowProps };\n"], "names": ["Arrow", "Root", "React", "Primitive", "NAME", "forwardRef", "props", "forwardedRef", "children", "width", "height", "arrowProps", "<PERSON><PERSON><PERSON><PERSON>"], "version": 3, "file": "index.mjs.map"}