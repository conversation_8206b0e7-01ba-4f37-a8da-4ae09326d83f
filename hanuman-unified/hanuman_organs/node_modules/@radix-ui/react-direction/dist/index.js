var $9g4ps$react = require("react");

function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "useDirection", () => $cc45c1b701a63adc$export$b39126d51d94e6f3);
$parcel$export(module.exports, "Provider", () => $cc45c1b701a63adc$export$2881499e37b75b9a);
$parcel$export(module.exports, "DirectionProvider", () => $cc45c1b701a63adc$export$c760c09fdd558351);

const $cc45c1b701a63adc$var$DirectionContext = /*#__PURE__*/ $9g4ps$react.createContext(undefined);
/* -------------------------------------------------------------------------------------------------
 * Direction
 * -----------------------------------------------------------------------------------------------*/ const $cc45c1b701a63adc$export$c760c09fdd558351 = (props)=>{
    const { dir: dir , children: children  } = props;
    return /*#__PURE__*/ $9g4ps$react.createElement($cc45c1b701a63adc$var$DirectionContext.Provider, {
        value: dir
    }, children);
};
/* -----------------------------------------------------------------------------------------------*/ function $cc45c1b701a63adc$export$b39126d51d94e6f3(localDir) {
    const globalDir = $9g4ps$react.useContext($cc45c1b701a63adc$var$DirectionContext);
    return localDir || globalDir || 'ltr';
}
const $cc45c1b701a63adc$export$2881499e37b75b9a = $cc45c1b701a63adc$export$c760c09fdd558351;




//# sourceMappingURL=index.js.map
