{"mappings": ";AAEA,iBAAiB,KAAK,GAAG,KAAK,CAAC;AAO/B;IACE,QAAQ,CAAC,EAAE,MAAM,SAAS,CAAC;IAC3B,GAAG,EAAE,SAAS,CAAC;CAChB;AACD,OAAA,MAAM,mBAAmB,MAAM,EAAE,CAAC,sBAAsB,CAGvD,CAAC;AAIF,6BAAsB,QAAQ,CAAC,EAAE,SAAS,aAGzC;AAED,OAAA,MAAM,0CAA4B,CAAC", "sources": ["packages/react/direction/src/packages/react/direction/src/Direction.tsx", "packages/react/direction/src/packages/react/direction/src/index.ts", "packages/react/direction/src/index.ts"], "sourcesContent": [null, null, "export {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n} from './Direction';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}