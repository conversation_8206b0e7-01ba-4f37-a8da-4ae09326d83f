{"mappings": ";;;AAOA,iBAAiB,MAAM,wBAAwB,CAAC,WAAW,CAAC,CAAC;AAE7D,gCAA0B,SAAQ,SAAS;IACzC,KAAK,EAAE,GAAG,CAAC;CACZ;AAOD,iCAA0B,WAAW,SAAS,WAAW,EAAE,QAAQ,GAAG,EAAE,EAAE,IAAI,EAAE,MAAM;;mBAkBpC,MAAM,SAAS;eAAS,GAAG;;;;kBAsC/D,MAAM,SAAS;eAClB,GAAG;;WA6BkB,GAAG;SA5EmB,MAAM,SAAS,CAAC,WAAW,CAAC;kFAkGjF", "sources": ["packages/react/collection/src/packages/react/collection/src/Collection.tsx", "packages/react/collection/src/packages/react/collection/src/index.ts", "packages/react/collection/src/index.ts"], "sourcesContent": [null, null, "export { createCollection } from './Collection';\nexport type { CollectionProps } from './Collection';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}