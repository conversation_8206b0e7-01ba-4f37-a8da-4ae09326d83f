{"mappings": ";;;;;ACAA,SAASA,yCAAT,CAAeC,KAAf,EAA8B,CAACC,GAAD,EAAMC,GAAN,CAA9B,EAAoE;IAClE,OAAOC,IAAI,CAACF,GAAL,CAASC,GAAT,EAAcC,IAAI,CAACD,GAAL,CAASD,GAAT,EAAcD,KAAd,CAAd,CAAP,CAAA;CACD;;ADFD", "sources": ["packages/core/number/src/index.ts", "packages/core/number/src/number.ts"], "sourcesContent": ["export { clamp } from './number';\n", "function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n"], "names": ["clamp", "value", "min", "max", "Math"], "version": 3, "file": "index.js.map"}